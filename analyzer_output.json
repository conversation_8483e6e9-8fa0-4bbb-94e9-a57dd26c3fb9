[{"name": "COMPLEX Search - User Sprint Performance Analysis", "query": "Show me all issues assigned to <PERSON><PERSON> in active sprints with their current status and priority", "search_type": "user_sprint_performance", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "<PERSON><PERSON><PERSON><PERSON>", "filters": [{"property_name": "name", "condition": {"operator": "=", "value": "<PERSON><PERSON><PERSON>uma<PERSON>", "value2": null}, "original_field": null}], "properties_to_return": ["id", "name", "email"]}, {"node_type": "Issue", "filters": [], "properties_to_return": ["key", "issue_type", "status", "priority", "created_at", "updated_at"]}, {"node_type": "Sprint", "filters": [{"property_name": "state", "condition": {"operator": "=", "value": "active", "value2": null}, "original_field": null}], "properties_to_return": ["id", "name", "state", "start_date", "end_date", "goal"]}]}, "expected_min_results": 5, "description": "Find a specific user, traverse to their assigned issues, then check which of those issues are in active sprints. Requires joining JiraUser -> ASSIGNED_TO -> Issue -> IN_SPRINT -> Sprint"}, {"name": "COMPLEX Search - Project Health Dashboard", "query": "Find all high priority bugs in project WEBDEV that are overdue and show who reported them", "search_type": "project_health_analysis", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Project", "filters": [{"property_name": "key", "condition": {"operator": "=", "value": "ARW", "value2": null}, "original_field": null}], "properties_to_return": ["key", "name", "id"]}, {"node_type": "Issue", "filters": [{"property_name": "issue_type", "condition": {"operator": "=", "value": "Bug", "value2": null}, "original_field": null}, {"property_name": "priority", "condition": {"operator": "=", "value": "High", "value2": null}, "original_field": null}, {"property_name": "due_date", "condition": {"operator": "<", "value": "2025-08-04", "value2": null}, "original_field": null}, {"property_name": "status", "condition": {"operator": "!=", "value": "Done", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "priority", "due_date", "created_at"]}, {"node_type": "<PERSON><PERSON><PERSON><PERSON>", "filters": [], "properties_to_return": ["id", "name", "email"]}]}, "expected_min_results": 3, "description": "Start from a specific project, find all its issues matching bug/high priority/overdue criteria, then traverse to find who reported each issue. Uses Project -> HAS_ISSUE -> Issue -> REPORTED_TO -> JiraUser path"}, {"name": "COMPLEX Search - Cross-Project Epic Analysis", "query": "Show me all epics created in the last 30 days across all projects with their child stories and current progress", "search_type": "epic_hierarchy_analysis", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Issue", "filters": [{"property_name": "issue_type", "condition": {"operator": "=", "value": "Epic", "value2": null}, "original_field": null}, {"property_name": "created_at", "condition": {"operator": ">=", "value": "2025-07-05", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "progress", "created_at", "project_key"]}, {"node_type": "Issue", "filters": [{"property_name": "issue_type", "condition": {"operator": "IN", "value": "Story,Task", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "progress", "parent_key"]}, {"node_type": "Project", "filters": [], "properties_to_return": ["key", "name"]}]}, "expected_min_results": 2, "description": "Find recent epics, then traverse to find their child issues using the HAS_PARENT relationship in reverse, and include project information. Complex hierarchy traversal with date filtering"}, {"name": "COMPLEX Search - Team Workload Distribution", "query": "Find all users in project MOBILE who have more than 5 open issues assigned and show their task breakdown by issue type", "search_type": "team_workload_analysis", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Project", "filters": [{"property_name": "key", "condition": {"operator": "=", "value": "MOBILE", "value2": null}, "original_field": null}], "properties_to_return": ["key", "name"]}, {"node_type": "<PERSON><PERSON><PERSON><PERSON>", "filters": [], "properties_to_return": ["id", "name", "email"]}, {"node_type": "Issue", "filters": [{"property_name": "status", "condition": {"operator": "IN", "value": "To Do,In Progress,In Review", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "priority"]}]}, "expected_min_results": 2, "description": "Start from project, find users with access, then count their assigned open issues and filter for heavy workloads. Requires aggregation logic in the query builder to count issues per user"}, {"name": "COMPLEX Search - Sprint Velocity Tracking", "query": "Show me completed issues in sprint 'Sprint 23' with story points and completion dates for velocity calculation", "search_type": "sprint_velocity_analysis", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Sprint", "filters": [{"property_name": "name", "condition": {"operator": "=", "value": "Sprint 23", "value2": null}, "original_field": null}], "properties_to_return": ["id", "name", "state", "start_date", "end_date", "complete_date"]}, {"node_type": "Issue", "filters": [{"property_name": "status", "condition": {"operator": "=", "value": "Done", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "updated_at"]}]}, "expected_min_results": 10, "description": "Find a specific sprint and all completed issues within it, including story points from the relationship properties. Uses Sprint <- IN_SPRINT <- Issue traversal with status filtering"}, {"name": "SIMPLE Search - User's Recent Activity", "query": "What issues has <PERSON> worked on in the last week?", "search_type": "user_recent_activity", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "<PERSON><PERSON><PERSON><PERSON>", "filters": [{"property_name": "name", "condition": {"operator": "=", "value": "<PERSON>", "value2": null}, "original_field": null}], "properties_to_return": ["id", "name"]}, {"node_type": "Issue", "filters": [{"property_name": "updated_at", "condition": {"operator": ">=", "value": "2025-07-28", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "updated_at", "project_key"]}]}, "expected_min_results": 3, "description": "Simple user-to-issues traversal with recent activity filtering. Uses JiraUser -> ASSIGNED_TO -> Issue path with date constraint"}, {"name": "COMPLEX Search - Blocked Issues Analysis", "query": "Find all issues that are blocked or have dependencies and show their impact on sprint delivery", "search_type": "dependency_impact_analysis", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Issue", "filters": [{"property_name": "status", "condition": {"operator": "=", "value": "Blocked", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "priority", "due_date", "project_key"]}, {"node_type": "Issue", "filters": [], "properties_to_return": ["key", "issue_type", "parent_key"]}, {"node_type": "Sprint", "filters": [{"property_name": "state", "condition": {"operator": "IN", "value": "active,future", "value2": null}, "original_field": null}], "properties_to_return": ["id", "name", "state", "end_date"]}]}, "expected_min_results": 1, "description": "Find blocked issues, their dependent issues through HAS_PARENT relationships, and which sprints they impact. Complex dependency chain analysis"}, {"name": "COMPLEX Search - Project Milestone Tracking", "query": "Show me all epics in project DATA that are due this month and their completion status with assigned teams", "search_type": "milestone_tracking", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Project", "filters": [{"property_name": "key", "condition": {"operator": "=", "value": "DATA", "value2": null}, "original_field": null}], "properties_to_return": ["key", "name"]}, {"node_type": "Issue", "filters": [{"property_name": "issue_type", "condition": {"operator": "=", "value": "Epic", "value2": null}, "original_field": null}, {"property_name": "due_date", "condition": {"operator": "BETWEEN", "value": "2025-08-01", "value2": "2025-08-31"}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "progress", "due_date"]}, {"node_type": "<PERSON><PERSON><PERSON><PERSON>", "filters": [], "properties_to_return": ["id", "name"]}]}, "expected_min_results": 2, "description": "Project-based epic filtering with date range constraints and team assignment traversal. Uses Project -> HAS_ISSUE -> Issue -> ASSIGNED_TO -> <PERSON><PERSON><PERSON><PERSON>"}, {"name": "SIMPLE Search - Issue Status Update", "query": "Find all issues that were moved to Done status today", "search_type": "daily_completion_tracking", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Issue", "filters": [{"property_name": "status", "condition": {"operator": "=", "value": "Done", "value2": null}, "original_field": null}, {"property_name": "updated_at", "condition": {"operator": ">=", "value": "2025-08-04", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "updated_at", "project_key"]}]}, "expected_min_results": 5, "description": "Simple status and date-based filtering for daily completion tracking. Direct Issue node filtering without complex traversals"}, {"name": "COMPLEX Search - Resource Planning Analysis", "query": "Find all users who have access to multiple projects and show their current issue load across projects", "search_type": "resource_planning", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "<PERSON><PERSON><PERSON><PERSON>", "filters": [], "properties_to_return": ["id", "name", "email"]}, {"node_type": "Project", "filters": [], "properties_to_return": ["key", "name"]}, {"node_type": "Issue", "filters": [{"property_name": "status", "condition": {"operator": "IN", "value": "To Do,In Progress,In Review", "value2": null}, "original_field": null}], "properties_to_return": ["key", "issue_type", "status", "project_key"]}]}, "expected_min_results": 3, "description": "Multi-project user analysis requiring aggregation of project access and issue counts. Uses JiraUser -> HAS_ACCESS -> Project and JiraUser -> ASSIGNED_TO -> Issue traversals with counting logic"}, {"name": "Issues assigned to user with status filter", "query": "Show me all issues assigned to <PERSON> that are in progress", "search_type": "Assigned Issues by Status", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "<PERSON><PERSON><PERSON><PERSON>", "filters": [{"property_name": "name", "condition": {"operator": "=", "value": "<PERSON>", "value2": null}, "original_field": null}], "properties_to_return": ["id", "name"]}, {"node_type": "Issue", "filters": [{"property_name": "status", "condition": {"operator": "=", "value": "In Progress", "value2": null}, "original_field": null}], "properties_to_return": ["key", "summary", "status", "priority"]}]}, "expected_min_results": 1, "description": "Traverse from JiraUser to Issue using ASSIGNED_TO and filter issues by status"}, {"name": "High-priority issues in a project with due date filter", "query": "List all high-priority issues in project PROJ1 that are due before August 15, 2025", "search_type": "Project Issues by Priority and Due Date", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Project", "filters": [{"property_name": "key", "condition": {"operator": "=", "value": "PROJ1", "value2": null}, "original_field": null}], "properties_to_return": ["key", "name"]}, {"node_type": "Issue", "filters": [{"property_name": "priority", "condition": {"operator": "=", "value": "High", "value2": null}, "original_field": null}, {"property_name": "due_date", "condition": {"operator": "<", "value": "2025-08-15", "value2": null}, "original_field": null}], "properties_to_return": ["key", "summary", "due_date", "priority"]}]}, "expected_min_results": 3, "description": "Use HAS_ISSUE relationship to find Issues in a Project and apply priority and due date filters"}, {"name": "Open Issues in a Sprint", "query": "Show me all open issues in Sprint 23", "search_type": "Sprint-based Issue Retrieval", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Sprint", "filters": [{"property_name": "name", "condition": {"operator": "=", "value": "Sprint 23", "value2": null}, "original_field": null}], "properties_to_return": ["id", "name", "state"]}, {"node_type": "Issue", "filters": [{"property_name": "status", "condition": {"operator": "!=", "value": "Done", "value2": null}, "original_field": null}], "properties_to_return": ["key", "summary", "status"]}]}, "expected_min_results": 5, "description": "Traverse from Sprint to Issues via IN_SPRINT relationship and filter out completed issues"}, {"name": "Recent Epics", "query": "Show epics created in the last 7 days", "search_type": "Recent Issues by Type", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Issue", "filters": [{"property_name": "issue_type", "condition": {"operator": "=", "value": "Epic", "value2": null}, "original_field": null}, {"property_name": "created_at", "condition": {"operator": ">=", "value": "2025-07-28", "value2": null}, "original_field": null}], "properties_to_return": ["key", "summary", "created_at"]}]}, "expected_min_results": 1, "description": "Filter Issues by issue_type=Epic and created_at in the past 7 days"}, {"name": "Sub-tasks under a parent issue", "query": "Show all sub-tasks under PROJ-101", "search_type": "Issue Hierarchy Traversal", "strategy": "SearchStrategy.GRAPH_TRAVERSAL", "filters": {"nodes": [{"node_type": "Issue", "filters": [{"property_name": "key", "condition": {"operator": "=", "value": "PROJ-101", "value2": null}, "original_field": null}], "properties_to_return": ["key", "summary"]}, {"node_type": "Issue", "filters": [{"property_name": "issue_type", "condition": {"operator": "=", "value": "sub-task", "value2": null}, "original_field": null}], "properties_to_return": ["key", "summary", "status"]}]}, "expected_min_results": 2, "description": "Use HAS_PARENT to find all sub-task issues under the parent issue PROJ-101"}]