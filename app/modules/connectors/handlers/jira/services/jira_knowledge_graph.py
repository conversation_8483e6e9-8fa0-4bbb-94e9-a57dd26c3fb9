"""
Service for building Jira knowledge graph.
"""

import uuid
import time
import random
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Callable
import structlog
from functools import wraps
from app.services.neo4j_service import execute_read_query, execute_write_query
from app.utils.constants.sources import SourceType
from app.modules.connectors.handlers.jira.services.jira_client import JiraClient
from app.modules.connectors.handlers.jira.repository.jira_queries import Jira<PERSON><PERSON>y
from app.modules.connectors.handlers.jira.utils.descToMd import DocumentConverter
from app.modules.connectors.handlers.jira.utils.commentsToMd import JiraCommentsToMarkdownConverter
from app.utils.pinecone.pinecone_service import PineconeService

import os
import json

logger = structlog.get_logger()

class JiraKnowledgeGraphService:
    """
    Service for building Jira knowledge graph.
    
    This service is responsible for building a knowledge graph of Jira projects,
    issues, and comments for a given organization.
    """
    
    def __init__(self):
        """Initialize the service with necessary repositories and services."""
        self.jira_query = JiraQuery()
        self.pinecone_service = PineconeService()
        self.jira_client = None
        # Set to track processed users to avoid redundant database operations
        self.processed_users = set()
        # Maximum number of retries for operations
        self.max_retries = 3
        # Base delay for exponential backoff (in seconds)
        self.base_delay = 1
        # Track failed operations for later recovery
        self.failed_operations = []
        
        # Initialize the comments repository for PostgreSQL operations
        from app.modules.connectors.handlers.jira.repository.jira_comments_repository import JiraCommentsRepository
        self.comments_repository = JiraCommentsRepository()
    
    def build_knowledge_graph(self, organisation_id: str, project_keys: List[str]) -> Tuple[bool, str]:
        """
        Build a knowledge graph for Jira projects.
        
        Args:
            organisation_id: ID of the organization
            projects_key: List of project keys to index. If ["*"], all projects will be indexed.
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Reset the processed users set at the beginning of each build
            self.processed_users = set()
            
            start_time = datetime.now()
            logger.info(f"Starting knowledge graph build at {start_time}")

            # Check if Jira source exists for the organization
            res = execute_read_query(self.jira_query.GET_JIRA_SOURCE, { "org_id": organisation_id, "source_type": SourceType.JIRA.value })
            if not res:
                return False, "Jira source not found for the organization"
                
            jira_source = res[0]['s']

            if not jira_source:
                return False, "Jira source not found for the organization"
            
            # Extract credentials from the source node
            jira_url = jira_source.get("jira_url")
            jira_email = jira_source.get("jira_email")
            api_token = jira_source.get("key")
            
            if not jira_url or not jira_email or not api_token:
                return False, "Invalid Jira credentials in source node"
            
            # Initialize Jira client
            self.jira_client = JiraClient(base_url=jira_url, username=jira_email, api_token=api_token)
            
            # Extract and load projects
            all_projects = self.jira_client.get_projects()
            logger.info(f"Found {len(all_projects)} projects")

            normalized_keys = [k.upper() for k in project_keys]
            filtered_projects = []
        
            for project in all_projects:
                project_key = project.get('key', "").upper()
                if "*" in normalized_keys or project_key in normalized_keys:
                    filtered_projects.append(project)
            
            logger.info(f"Processing {len(filtered_projects)} filtered projects")

            if not filtered_projects:
                return True, "No projects found matching the specified keys"
            
            # Batch create all project nodes
            success_count = self.create_project_nodes_batch(filtered_projects, organisation_id)
            
            if success_count == 0:
                return False, "Failed to create any project nodes"
            
            # Extract users for all successfully created projects
            successful_project_keys = [p.get('key') for p in filtered_projects[:success_count]]
            self.extract_users_for_projects_batch(successful_project_keys, organisation_id)
            
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(f"Knowledge graph build completed at {end_time}")
            logger.info(f"Total duration: {duration}")
            
            return True, f"Successfully built knowledge graph for {len(project_keys)} projects"
            
        except Exception as e:
            error_msg = f"Error building Jira knowledge graph: {e}"
            logger.error(error_msg)
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "build_knowledge_graph",
                "params": {
                    "organisation_id": organisation_id,
                    "project_keys": project_keys
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return False, error_msg
            
    def _process_issue_comments(
        self,
        fields: Dict[str, Any],
        organisation_id: str,
        issue_key: str
    ) -> None:
        """
        Process comments for an issue.
        
        Args:
            fields: Issue fields containing comments
            organisation_id: ID of the organization
            issue_key: Key of the issue
        """
        try:
            comments_data = fields.get("comment") or {}

            # Convert comments to markdown
            comments = JiraCommentsToMarkdownConverter().convert_comments(comments_data)
            
            # Get project key from issue key
            project_key = issue_key.split("-")[0]
            
            # Process each comment
            # for comment in comments:
            #     self._store_comment(comment, organisation_id, project_key, issue_key)
                
        except Exception as e:
            logger.error(f"Error processing issue comments for {issue_key}: {e}")
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "_process_issue_comments",
                "params": {
                    "issue_key": issue_key,
                    "organisation_id": organisation_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    def _store_comment(self, comment: Dict[str, Any], organisation_id: str, project_key: str, issue_key: str) -> None:
        """
        Store a single comment in the PostgreSQL database.
        
        Args:
            comment: Comment data
            organisation_id: ID of the organization
            project_key: Key of the project
            issue_key: Key of the issue
        """
        comment_id = comment.get("comment_id", "")
        if not comment_id:
            comment_id = str(uuid.uuid4())
        
        logger.debug(f"Creating comment node {comment_id} for issue {issue_key}")
        
        try:
            author = comment.get("author", {})
            user_email = author.get("emailAddress", "")
            user_id = author.get("accountId", "")
            
            # Parse comment date
            comment_date = self._parse_comment_date(comment.get("created_at", ""))
            
            # Store comment in PostgreSQL
            self.comments_repository.create_comment(
                organisation_id=organisation_id,
                project_key=project_key,
                issue_key=issue_key,
                comment_date=comment_date,
                comment=comment.get("comment", ""),
                user_email=user_email,
                user_id=user_id
            )
            
            logger.info(f"Stored comment {comment_id} for issue {issue_key} in PostgreSQL database")
        except Exception as db_error:
            logger.error(f"Error storing comment in PostgreSQL: {db_error}",
                        issue_key=issue_key,
                        comment_id=comment_id,
                        error=str(db_error),
                        error_type=type(db_error).__name__)
    
    def _parse_comment_date(self, created_at_str: str) -> datetime:
        """
        Parse a comment date string into a datetime object.
        
        Args:
            created_at_str: Date string to parse
            
        Returns:
            datetime: Parsed datetime object or current time if parsing fails
        """
        if not created_at_str:
            return datetime.now()
            
        try:
            return datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
        except ValueError:
            # If parsing fails, use current time
            return datetime.now()
            
    async def _process_issue_blockers(
        self,
        jira_client: JiraClient,
        organisation_id: str,
        issue_key: str
    ) -> None:
        """
        Process blockers for an issue.
        
        Args:
            jira_client: Initialized Jira client
            organisation_id: ID of the organization
            issue_key: Key of the issue
        """
        try:
            # Get issue details with links
            issue = jira_client.get_issue(issue_key, expand=["issuelinks"])
            
            # Process issue links
            links = issue.get("fields", {}).get("issuelinks", [])
            
            for link in links:
                link_type = link.get("type", {}).get("name", "").lower()
                
                # Check if this is a blocker relationship
                if link_type == "blocks" or "block" in link_type:
                    # Outward link means this issue blocks another
                    if "outwardIssue" in link:
                        blocked_key = link["outwardIssue"]["key"]
                        
                        # Create blocker relationship
                        execute_write_query(
                            self.jira_query.CREATE_ISSUE_BLOCKER_RELATIONSHIP,
                            {
                                "blocker_key": issue_key,
                                "blocked_key": blocked_key,
                                "organisation_id": organisation_id
                            }
                        )
                    
                    # Inward link means this issue is blocked by another
                    elif "inwardIssue" in link:
                        blocker_key = link["inwardIssue"]["key"]
                        
                        # Create blocker relationship
                        execute_write_query(
                            self.jira_query.CREATE_ISSUE_BLOCKER_RELATIONSHIP,
                            {
                                "blocker_key": blocker_key,
                                "blocked_key": issue_key,
                                "organisation_id": organisation_id
                            }
                        )
                
        except Exception as e:
            logger.error(f"Error processing issue blockers for {issue_key}: {e}")
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "_process_issue_blockers",
                "params": {
                    "issue_key": issue_key,
                    "organisation_id": organisation_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
    async def _process_project_sprints(
        self,
        jira_client: JiraClient,
        organisation_id: str,
        project_key: str
    ) -> None:
        """
        Process sprints for a project.
        
        Args:
            jira_client: Initialized Jira client
            organisation_id: ID of the organization
            project_key: Key of the project
        """
        try:
            # Get boards for the project
            boards = jira_client.get_boards(projectKeyOrId=project_key)
            
            for board in boards.get("values", []):
                board_id = board.get("id")
                
                # Get sprints for the board
                sprints = jira_client.get_sprints(board_id)
                
                for sprint in sprints.get("values", []):
                    sprint_id = sprint.get("id")
                    
                    # Create sprint node
                    execute_write_query(
                        self.jira_query.CREATE_OR_UPDATE_SPRINT,
                        {
                            "id": sprint_id,
                            "organisation_id": organisation_id,
                            "project_key": project_key,
                            "name": sprint.get("name", ""),
                            "state": sprint.get("state", ""),
                            "start_date": sprint.get("startDate", ""),
                            "end_date": sprint.get("endDate", ""),
                            "complete_date": sprint.get("completeDate", ""),
                            "goal": sprint.get("goal", ""),
                            "board_id": board_id
                        }
                    )
                    
                    # Get issues for the sprint
                    sprint_issues = jira_client.get_sprint_issues(sprint_id)
                    
                    for issue in sprint_issues.get("issues", []):
                        issue_key = issue.get("key")
                        
                        # Link issue to sprint
                        execute_write_query(
                            self.jira_query.LINK_ISSUE_TO_SPRINT,
                            {
                                "issue_key": issue_key,
                                "sprint_id": sprint_id,
                                "organisation_id": organisation_id
                            }
                        )
                
        except Exception as e:
            logger.error(f"Error processing project sprints for {project_key}: {e}")
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "_process_project_sprints",
                "params": {
                    "project_key": project_key,
                    "organisation_id": organisation_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    def create_project_nodes_batch(self, projects: List[Dict[str, Any]], org_id: str) -> int:
        """
        Create multiple Project nodes in Neo4j using batch processing.
        
        Args:
            projects: List of project data from Jira
            org_id: Organization ID
            
        Returns:
            int: Number of projects successfully created
        """
        if not projects:
            return 0
        
        # Prepare batch data
        project_params = []
        for project in projects:
            project_params.append({
                "key": project.get("key"),
                "name": project.get("name"),
                "id": project.get("id"),
                "entity_id": project.get("entityId", ""),
                "uuid": project.get("uuid", "")
            })
        
        params = {
            "projects": project_params,
            "org_id": org_id,
            "type": SourceType.JIRA.value
        }
        
        try:
            # Execute batch creation
            execute_write_query(self.jira_query.CREATE_JIRA_PROJECTS_BATCH, params)
            success_count = len(project_params)
            logger.info(f"Successfully created {success_count} project nodes in batch")
            return success_count
            
        except Exception as e:
            logger.error(f"Error creating project nodes in batch: {str(e)}")
            # Fallback to individual creation for partial success
            return self.create_project_nodes_individual_fallback(projects, org_id)

    def create_project_nodes_individual_fallback(self, projects: List[Dict[str, Any]], org_id: str) -> int:
        """
        Fallback method to create project nodes individually if batch fails.
        
        Args:
            projects: List of project data from Jira
            org_id: Organization ID
            
        Returns:
            int: Number of projects successfully created
        """
        success_count = 0
        logger.warning("Falling back to individual project creation")
        
        for project in projects:
            if self.create_project_node_individual(project, org_id):
                success_count += 1
            else:
                logger.error(f"Failed to create project node for {project.get('key')}")
        
        return success_count

    def create_project_node_individual(self, project: Dict[str, Any], org_id: str) -> bool:
        """
        Create a single Project node in Neo4j (fallback method).
        
        Args:
            project: Project data from Jira
            org_id: Organization ID
            
        Returns:
            bool: True if project was created successfully, False otherwise
        """
        params = {
            "key": project.get("key"),
            "name": project.get("name"),
            "id": project.get("id"),
            "entity_id": project.get("entityId", ""),
            "uuid": project.get("uuid", ""),
            "org_id": org_id,
            "type": SourceType.JIRA.value
        }

        try:
            execute_write_query(self.jira_query.CREATE_JIRA_PROJECT, params)
            return True
        except Exception as e:
            logger.error(f"Error creating project node for {project.get('key')}: {str(e)}")
            return False
        
    def extract_users_for_projects_batch(self, project_keys: List[str], org_id: str) -> None:
        """
        Extract users for multiple projects efficiently.
        
        Args:
            project_keys: List of project keys to extract users for
            org_id: Organization ID
        """
        # Process projects in chunks to avoid overwhelming the API
        chunk_size = 5  # Adjust based on your API rate limits
        
        for i in range(0, len(project_keys), chunk_size):
            chunk = project_keys[i:i + chunk_size]
            logger.info(f"Extracting users for projects chunk {i//chunk_size + 1}: {chunk}")
            
            # Process chunk in parallel if your extract_project_users method supports it
            # or modify extract_project_users to handle multiple projects
            for project_key in chunk:
                try:
                    self.extract_project_users(project_key, org_id)
                except Exception as e:
                    logger.error(f"Error extracting users for project {project_key}: {e}")
                    continue

    def extract_project_users(self, project_key: str, org_id: str):
        """
        Extract all users associated with a project and load them into Neo4j.
        
        Args:
            project_key: Key of the project to extract users from
        """
        logger.info(f"Extracting users for project {project_key}...")
        
        # First try to get users from project roles
        # if self.extract_project_role_users(project_key):
        #     logger.info(f"Successfully extracted users from project roles for {project_key}")

        
        # Get all issues with comments (using a simpler JQL query)
        # Handle reserved keywords in JQL by quoting the project key
        issues_jql = f'project = "{project_key}" ORDER BY key ASC'
        issues = self.jira_client.get_all_issues(
            jql=issues_jql,
            fields=["*all"],
            max_results_per_page=100,
            max_pages=100
        )
        
        logger.info(f"Fetched {len(issues)} issues for project {project_key}")

        # with open(f"./all_issues_{project_key}.json", "w", encoding="utf-8") as f:
        #         json.dump(issues, f, indent=2)
        
        # Process all issues to build the knowledge graph
        logger.info("Building knowledge graph from issues")
        for issue in issues:
            issue_key = issue.get("key")
            project_key = issue_key.split("-")[0]
            
            fields = issue.get("fields", {})

            # STEP 2: Convert description to text
            description_field = fields.get("description") or {}
            description = DocumentConverter().convert_to_text(description_field)

            # STEP 3: Create issue node
            logger.info(f"Creating issue node in Neo4j: {issue_key}")
            self.create_issue_node(issue, project_key, description, org_id)

            self._process_issue_comments(fields, org_id, issue_key)
            

    def create_user_node(self, user: Dict[str, Any], org_id: str):
        """
        Create a User node in Neo4j.
        
        Args:
            user: User data from Jira
            org_id: Organization ID
        """
        # Check if we have a valid user with an account ID
        if not user or not user.get("accountId"):
            logger.warning("Skipping user creation: Missing or invalid user data")
            return

        account_id = user.get("accountId", "")
        
        # Check if we've already processed this user in the current run
        if account_id in self.processed_users:
            logger.debug(f"Skipping already processed user: {user.get('displayName', 'Unknown User')} ({account_id})")
            return
            
        # Add to processed users set
        self.processed_users.add(account_id)
        
        email = user.get("emailAddress", "")
        
        params = {
            "accountId": account_id,
            "name": user.get("displayName", "Unknown User"),
            "email": email,
            "org_id": org_id
        }
        
        try:
            # Use email as primary identifier if available, otherwise use accountId
            if email and email != "":
                execute_write_query(self.jira_query.CREATE_USER_NODE_WITH_EMAIL, params)
            else:
                execute_write_query(self.jira_query.CREATE_USER_NODE_WITHOUT_EMAIL, params)
                
            logger.debug(f"Created/updated user node for {params['name']} ({params['accountId']})")
        except Exception as e:
            logger.error(f"Error creating user node for account ID {user.get('accountId')}: {str(e)}")

    def create_issue_node(self, issue: Dict[str, Any], project_key: str, description: str, org_id: str):
        """
        Create an Issue node in Neo4j and link it to its Project.
        
        Args:
            issue: Issue data from Jira
            project_key: Key of the project this issue belongs to
            description: Description for the issue
            org_id: Organization ID
        """
        fields = issue.get("fields", {})

        # Check if parent exists
        has_parent = fields.get("parent", {}).get("key") is not None
        
        # Extract status and issue type names safely
        status = fields.get("status", {}).get("name", "") if fields.get("status") else ""
        issue_type = fields.get("issuetype", {}).get("name", "") if fields.get("issuetype") else ""
        priority = fields.get("priority", {}).get("name", "") if fields.get("priority") else ""
        
        # Handle progress safely
        progress_data = fields.get("progress", {})
        progress_value = progress_data.get("progress", 0) if progress_data else 0
        
        # Handle aggregateprogress safely
        aggregate_progress_data = fields.get("aggregateprogress", {})
        aggregate_progress_value = aggregate_progress_data.get("percent", 0) if aggregate_progress_data else 0
        
        # Handle storypoints (customfield_10016) safely
        storypoints = fields.get("customfield_10016", 0) or 0
        
        # Handle dates safely
        due_date = fields.get("duedate") or ""
        created_date = fields.get("created") or ""
        updated_date = fields.get("updated") or ""

        title = fields.get("summary", "")
        
        # Process worklog information
        worklog_data = fields.get("worklog", {})
        worklogs = worklog_data.get("worklogs", [])
        total_time_spent = 0
        
        # Sum up time spent from all worklog entries
        for worklog in worklogs:
            time_spent_seconds = worklog.get("timeSpentSeconds", 0) or 0
            total_time_spent += time_spent_seconds
        
        params = {
            "project_key": project_key,
            "key": issue.get("key"),
            "id": issue.get("id"),
            "status": status,
            "issueType": issue_type,
            "priority": priority,
            "title": title,
            "labels": fields.get("labels", []),
            "created": created_date,
            "updated": updated_date,
            "parent_key": fields.get("parent", {}).get("key"),
            "dueDate": due_date,
            "progress": str(progress_value),
            "aggregate_progress": str(aggregate_progress_value),
            "storypoints": str(storypoints),
            "timespent": str(total_time_spent)
        }
        
        try:
            # First, create sprints if they exist in the issue
            self._create_sprints_for_issue(fields, org_id, project_key)

            # Then create the issue node using the new CREATE_OR_UPDATE_ISSUE query
            issue_params = {
                "id": issue.get("id"),
                "key": issue.get("key"),
                "organisation_id": org_id,
                "project_key": project_key,
                # "summary": title,
                # "description": description,
                "issue_type": issue_type,
                "status": status,
                "priority": priority,
                "assignee_account_id": fields.get("assignee", {}).get("accountId", "") if fields.get("assignee") else "",
                "reporter_account_id": fields.get("reporter", {}).get("accountId", "") if fields.get("reporter") else "",
                "created_at": created_date,
                "updated_at": updated_date,
                # "vector_id": "",  # Will be set later if needed
                # "url": f"https://your-domain.atlassian.net/browse/{issue.get('key')}"
            }

            execute_write_query(self.jira_query.CREATE_OR_UPDATE_ISSUE, issue_params)

            # Finally, link the issue to sprints or project
            self._link_issue_to_sprints_or_project(fields, org_id, issue.get("key"))

            final_txt = f'''
            title: ${title} \n\n description: ${description}
            '''
            metadata = {
                "project_key": project_key,
                "key": issue.get("key"),
                "id": issue.get("id"),
                "name": title,
                "description": description or "",
                "dueDate": due_date,
                "status": status,
                "storypoints": storypoints,
                "timespent": total_time_spent
            }

            self.pinecone_service.upload_file_to_pinecone(None, org_id, metadata, final_txt, None, "jira")
        except Exception as e:
            logger.error(f"Error creating issue node {params['key']}: {str(e)}", 
                        issue_key=params['key'],
                        project_key=params['project_key'],
                        error=str(e))
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "create_issue_node",
                "params": {
                    "issue_key": params['key'],
                    "project_key": params['project_key'],
                    "organisation_id": org_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            raise
            
        # Create user nodes and relationships
        self.process_issue_users(issue, org_id)
        
        # Process worklog information in detail
        self._process_issue_worklog(fields, org_id, issue.get("key"))
        
    def _process_issue_worklog(self, fields: Dict[str, Any], organisation_id: str, issue_key: str) -> None:
        """
        Process worklog information from an issue in detail.
        
        Args:
            fields: Issue fields containing worklog information
            organisation_id: ID of the organization
            issue_key: Key of the issue
        """
        try:
            worklog_data = fields.get("worklog", {})
            if not worklog_data:
                return
                
            worklogs = worklog_data.get("worklogs", [])
            if not worklogs:
                return
                
            # Get project key from issue key
            project_key = issue_key.split("-")[0]
            
            # Update issue with detailed worklog information
            total_seconds = 0
            worklog_entries = []
            
            for worklog in worklogs:
                author = worklog.get("author", {})
                if author and author.get("accountId"):
                    # Create user node for the worklog author
                    self.create_user_node(author, organisation_id)
                
                # Calculate total time
                time_spent_seconds = worklog.get("timeSpentSeconds", 0)
                total_seconds += time_spent_seconds
                
                # Add worklog entry to the list
                worklog_entries.append({
                    "author": author.get("displayName", "Unknown"),
                    "author_id": author.get("accountId", ""),
                    "time_spent": worklog.get("timeSpent", ""),
                    "time_spent_seconds": time_spent_seconds,
                    "started": worklog.get("started", ""),
                    "created": worklog.get("created", ""),
                    "updated": worklog.get("updated", ""),
                    "comment": worklog.get("comment", "")
                })
            
            # Update issue node with worklog summary
            params = {
                "key": issue_key,
                "organisation_id": organisation_id,
                "total_time_spent_seconds": total_seconds,
                "worklog_count": len(worklogs),
                "worklog_details": json.dumps(worklog_entries)
            }
            
            # Execute query to update issue with worklog details
            # This would require a new query in jira_queries.py, but for now we'll just log it
            logger.info(f"Processed {len(worklogs)} worklog entries for issue {issue_key}")
            
        except Exception as e:
            logger.error(f"Error processing worklog information for issue {issue_key}: {e}")
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "_process_issue_worklog",
                "params": {
                    "issue_key": issue_key,
                    "organisation_id": organisation_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    def _process_issue_sprints(self, fields: Dict[str, Any], organisation_id: str, issue_key: str) -> None:
        """
        Process sprint information from customfield_10020 in an issue.
        
        Args:
            fields: Issue fields containing sprint information
            organisation_id: ID of the organization
            issue_key: Key of the issue
        """
        try:
            # Get sprint information from customfield_10020
            sprints = fields.get("customfield_10020", [])
            
            # Get project key from issue key
            project_key = issue_key.split("-")[0]
            
            # If no sprints, link issue directly to project
            if not sprints:
                logger.debug(f"No sprint information found for issue {issue_key}, linking directly to project")
                execute_write_query(
                    self.jira_query.LINK_ISSUE_TO_PROJECT,
                    {
                        "issue_key": issue_key,
                        "project_key": project_key,
                        "organisation_id": organisation_id
                    }
                )
                return
                
            # Process each sprint
            for sprint in sprints:
                if not sprint:
                    continue
                    
                sprint_id = sprint.get("id")
                if not sprint_id:
                    continue
                    
                # Create sprint node
                sprint_params = {
                    "id": sprint_id,
                    "organisation_id": organisation_id,
                    "project_key": project_key,
                    "name": sprint.get("name", ""),
                    "state": sprint.get("state", ""),
                    "start_date": sprint.get("startDate", ""),
                    "end_date": sprint.get("endDate", ""),
                    "complete_date": sprint.get("completeDate", ""),
                    "goal": sprint.get("goal", ""),
                    "board_id": sprint.get("boardId", "")
                }
                
                # Create sprint node and link it to project
                execute_write_query(
                    self.jira_query.CREATE_OR_UPDATE_SPRINT,
                    sprint_params
                )
                
                # Determine sprint status (open/closed based on state)
                sprint_state = sprint.get("state", "").lower()
                sprint_status = "closed" if sprint_state in ["closed", "complete"] else "open"

                # Link issue to sprint with status
                execute_write_query(
                    self.jira_query.LINK_ISSUE_TO_SPRINT,
                    {
                        "issue_key": issue_key,
                        "sprint_id": sprint_id,
                        "organisation_id": organisation_id,
                        "sprint_status": sprint_status
                    }
                )
                
                logger.info(f"Linked issue {issue_key} to sprint {sprint.get('name', 'Unknown')} (ID: {sprint_id})")
                
        except Exception as e:
            logger.error(f"Error processing sprint information for issue {issue_key}: {e}")
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "_process_issue_sprints",
                "params": {
                    "issue_key": issue_key,
                    "organisation_id": organisation_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    def _create_sprints_for_issue(self, fields: Dict[str, Any], organisation_id: str, project_key: str):
        """
        Create sprint nodes for an issue without linking the issue to them yet.

        Args:
            fields: Issue fields containing sprint information
            organisation_id: ID of the organization
            project_key: Key of the project
        """
        try:
            # First check if the project exists
            project_check = execute_read_query(
                self.jira_query.CHECK_PROJECT_EXISTS,
                {
                    "project_key": project_key,
                    "organisation_id": organisation_id
                }
            )

            if not project_check:
                logger.error(f"Project {project_key} not found when trying to create sprints. Organisation ID: {organisation_id}")
                return

            logger.debug(f"Project {project_key} exists, proceeding with sprint creation")

            # Get sprint information from customfield_10020
            sprints = fields.get("customfield_10020", [])

            if not sprints:
                logger.debug(f"No sprint information found for project {project_key}")
                return

            # Process each sprint to create sprint nodes
            for sprint in sprints:
                if not sprint:
                    continue

                sprint_id = sprint.get("id")
                if not sprint_id:
                    continue

                # Create sprint node
                sprint_params = {
                    "id": sprint_id,
                    "organisation_id": organisation_id,
                    "project_key": project_key,
                    "name": sprint.get("name", ""),
                    "state": sprint.get("state", ""),
                    "start_date": sprint.get("startDate", ""),
                    "end_date": sprint.get("endDate", ""),
                    "complete_date": sprint.get("completeDate", ""),
                    "goal": sprint.get("goal", ""),
                    "board_id": sprint.get("boardId", "")
                }

                # Create sprint node and link it to project
                result = execute_write_query(
                    self.jira_query.CREATE_OR_UPDATE_SPRINT,
                    sprint_params
                )

                if result and len(result) > 0:
                    sprint_node = result[0].get('s')
                    project_node = result[0].get('p')

                    if project_node:
                        logger.info(f"Successfully created/updated sprint {sprint.get('name', 'Unknown')} (ID: {sprint_id}) and linked to project {project_key}")
                    else:
                        logger.warning(f"Created/updated sprint {sprint.get('name', 'Unknown')} (ID: {sprint_id}) but could not link to project {project_key} - project not found")
                else:
                    logger.error(f"Failed to create sprint {sprint.get('name', 'Unknown')} (ID: {sprint_id}) for project {project_key}")

        except Exception as e:
            logger.error(f"Error creating sprints for project {project_key}: {e}")
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "_create_sprints_for_issue",
                "params": {
                    "project_key": project_key,
                    "organisation_id": organisation_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    def _link_issue_to_sprints_or_project(self, fields: Dict[str, Any], organisation_id: str, issue_key: str):
        """
        Link an issue to its sprints, or to the project if no sprints exist.

        Args:
            fields: Issue fields containing sprint information
            organisation_id: ID of the organization
            issue_key: Key of the issue
        """
        try:
            # Get sprint information from customfield_10020
            sprints = fields.get("customfield_10020", [])

            # Get project key from issue key
            project_key = issue_key.split("-")[0]

            # If no sprints, link issue directly to project
            if not sprints:
                logger.debug(f"No sprint information found for issue {issue_key}, linking directly to project")
                execute_write_query(
                    self.jira_query.LINK_ISSUE_TO_PROJECT,
                    {
                        "issue_key": issue_key,
                        "project_key": project_key,
                        "organisation_id": organisation_id
                    }
                )
                return

            # Link issue to each sprint
            for sprint in sprints:
                if not sprint:
                    continue

                sprint_id = sprint.get("id")
                if not sprint_id:
                    continue

                # Determine sprint status (open/closed based on state)
                sprint_state = sprint.get("state", "").lower()
                sprint_status = "closed" if sprint_state in ["closed", "complete"] else "open"

                # Link issue to sprint with status
                execute_write_query(
                    self.jira_query.LINK_ISSUE_TO_SPRINT,
                    {
                        "issue_key": issue_key,
                        "sprint_id": sprint_id,
                        "organisation_id": organisation_id,
                        "sprint_status": sprint_status
                    }
                )

                logger.info(f"Linked issue {issue_key} to sprint {sprint.get('name', 'Unknown')} (ID: {sprint_id}) with status: {sprint_status}")

        except Exception as e:
            logger.error(f"Error linking issue {issue_key} to sprints or project: {e}")
            # Store the failed operation for potential recovery
            self.failed_operations.append({
                "operation": "_link_issue_to_sprints_or_project",
                "params": {
                    "issue_key": issue_key,
                    "organisation_id": organisation_id
                },
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    def process_issue_users(self, issue: Dict[str, Any], org_id: str):
        """
        Process user information from an issue and create User nodes with relationships.
        
        Args:
            issue: Issue data from Jira
        """
        fields = issue.get("fields", {})
        issue_key = issue.get("key")
        project_key = issue_key.split("-")[0] if issue_key else ""
        
        # Process creator/reporter
        reporter = fields.get("reporter")
        if reporter and reporter.get("accountId"):
            try:
                self.create_user_node(reporter, org_id)
                self.create_user_issue_relationship(reporter.get("accountId"), issue_key, "HAS_REPORTED_TASKS")
                # Create relationship between Project and Access_projects
                if project_key:
                    self.create_project_access_relationship(reporter.get("accountId"), project_key)
            except Exception as e:
                logger.warning(f"Error processing reporter for issue {issue_key}: {str(e)}")
        
        # Process assignee
        assignee = fields.get("assignee")
        if assignee and assignee.get("accountId"):
            try:
                self.create_user_node(assignee, org_id)
                self.create_user_issue_relationship(assignee.get("accountId"), issue_key, "HAS_ASSIGNED_TASKS")
                # Create relationship between Project and Access_projects
                if project_key:
                    self.create_project_access_relationship(assignee.get("accountId"), project_key)
            except Exception as e:
                logger.warning(f"Error processing assignee for issue {issue_key}: {str(e)}")

    def create_user_issue_relationship(self, user_account_id: str, issue_key: str, relationship_type: str):
        """
        Create a relationship between a User and an Issue.
        
        Args:
            user_account_id: User's account ID
            issue_key: Issue key
            relationship_type: Type of relationship (e.g., REPORTED, ASSIGNED_TO)
        """
        if not user_account_id or not issue_key:
            logger.warning(f"Cannot create relationship: Missing user account ID or issue key")
            return
            
        try:
            params = {
                "user_account_id": user_account_id,
                "issue_key": issue_key
            }
            
            if relationship_type == "HAS_REPORTED_TASKS":
                result = execute_write_query(self.jira_query.CREATE_USER_ISSUE_RELATIONSHIP_REPORTED, params)
            elif relationship_type == "HAS_ASSIGNED_TASKS":
                result = execute_write_query(self.jira_query.CREATE_USER_ISSUE_RELATIONSHIP_ASSIGNED, params)
            
            if result:
                user_name = result[0]["u"].get("displayName", "Unknown")
                logger.debug(f"Created {relationship_type} relationship between user {user_name} ({user_account_id}) and issue {issue_key}")
                return True
            else:
                logger.warning(f"Could not create {relationship_type} relationship: User {user_account_id} or Issue {issue_key} not found")
                return False
                    
        except Exception as e:
            logger.error(f"Error creating relationship between user {user_account_id} and issue {issue_key}: {str(e)}")
            return False
    
    def extract_project_role_users(self, project_key: str) -> bool:
        """
        Extract users from project roles and add them to the knowledge graph.
        
        Args:
            project_key: Key of the project to extract role users from
            
        Returns:
            bool: True if successful, False if roles couldn't be accessed
        """
        logger.info(f"Extracting users from roles for project {project_key}...")
        
        # Check if we can access project roles
        try:
            if not self.jira_client.can_access_project_roles(project_key):
                logger.warning(f"Cannot access roles for project {project_key}")
                return False
                
            # Get all roles for the project
            roles = self.jira_client.get_project_roles(project_key)
            if not roles:
                logger.warning(f"No roles found for project {project_key}")
                return False
                
            logger.info(f"Found {len(roles)} roles for project {project_key}")
            
            # Process each role
            user_count = 0
            for role_name, role_url in roles.items():
                if role_name == "atlassian-addons-project-access":
                    continue
                # Extract role ID from URL
                role_id = role_url.split('/')[-1]
                
                try:
                    # Get role details including actors
                    role_details = self.jira_client.get_project_role(project_key, role_id)
                    
                    # Process actors (users)
                    actors = role_details.get("actors", [])
                    for actor in actors:
                        if actor.get("type") == "atlassian-user-role-actor":
                            user = actor.get("actorUser", {})
                            if user and user.get("accountId"):
                                # Create user node
                                self.create_user_node(user)

                                # Create relationship between Project and Access_projects
                                self.create_project_access_relationship(
                                    user.get("accountId"),
                                    project_key
                                )
                                user_count += 1
                except Exception as e:
                    logger.warning(f"Error processing role {role_name} for project {project_key}: {str(e)}")
                    continue
            
            logger.info(f"Extracted {user_count} users from roles for project {project_key}")
            return user_count > 0
            
        except Exception as e:
            logger.error(f"Error extracting users from roles for project {project_key}: {str(e)}")
            return False
    
    def create_project_access_relationship(self, user_account_id: str, project_key: str):
        """
        Create a relationship between a Project and an Access_projects node.
        
        Args:
            user_account_id: User's account ID
            project_key: Project key
        """
        if not user_account_id or not project_key:
            logger.warning(f"Cannot create project access relationship: Missing user account ID or project key")
            return
            
        try:
            params = {
                "user_account_id": user_account_id,
                "project_key": project_key
            }
            
            result = execute_write_query(self.jira_query.CREATE_PROJECT_ACCESS_RELATIONSHIP, params)
            
            if result:
                logger.debug(f"Created access relationship between user {user_account_id} and project {project_key}")
                return True
            else:
                logger.warning(f"Could not create access relationship: User {user_account_id} or Project {project_key} not found")
                return False
                    
        except Exception as e:
            logger.error(f"Error creating access relationship between user {user_account_id} and project {project_key}: {str(e)}")
            return False
        
