"""
Cypher Adapter for Jira Natural Language Search

This module provides an adapter for searching structured Jira data in Neo4j
using Cypher queries.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from app.services.neo4j_service import execute_read_query
from app.modules.connectors.handlers.jira.repository.jira_queries import JiraQ<PERSON>y

logger = logging.getLogger(__name__)

class CypherAdapter:
    """
    Adapter for searching structured Jira data in Neo4j.
    
    This class handles the conversion of structured query plans into
    Cypher queries for searching Jira data in Neo4j.
    """
    
    def __init__(self):
        """Initialize the Cypher adapter."""
        self.jira_query = JiraQuery()
        
        # Define search strategies
        # HERE TODO:
        self.SEARCH_STRATEGIES = {
            "SearchStrategy.GRAPH_TRAVERSAL": self._build_graph_traversal_query,
            "SearchStrategy.AGGREGATION": self._build_aggregation_query,
            "SearchStrategy.PATH_FINDING": self._build_path_finding_query
        }
    
    def search(self, query_plan: Dict[str, Any], max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Execute a Cypher search based on the query plan.
        
        Args:
            query_plan: The query plan generated by QueryPlanner
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Extract query parameters
            organisation_id = query_plan.get("organisation_id")
            project_key = query_plan.get("project_key")
            issue_key = query_plan.get("issue_key")
            user_email = query_plan.get("user_email")
            status = query_plan.get("status")
            priority = query_plan.get("priority")
            start_date = query_plan.get("start_date")
            end_date = query_plan.get("end_date")
            sprint = query_plan.get("sprint")
            
            if not organisation_id:
                logger.error("Missing required organisation_id parameter for Cypher search")
                return []
            
            # Determine the type of search to perform based on parameters
            if issue_key:
                return self._search_by_issue_key(issue_key, organisation_id, max_results)
            elif project_key and sprint:
                return self._search_by_sprint(project_key, sprint, organisation_id, max_results)
            elif project_key:
                return self._search_by_project(project_key, organisation_id, status, priority, max_results)
            elif user_email:
                return self._search_by_user(user_email, organisation_id, max_results)
            elif "metadata_query" in query_plan: # HERE TODO:
                return self.search_by_metadata_query(query_plan["metadata_query"], organisation_id, max_results)
            else:
                return self._search_by_criteria(query_plan, max_results)
            
        except Exception as e:
            logger.error(f"Error in Cypher search: {str(e)}")
            return []
        
    ########################################################################
    def search_by_metadata_query(self, metadata_query: Dict[str, Any], organisation_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Execute a Cypher search based on a structured metadata query.

        Args:
            metadata_query: The structured metadata query
            organisation_id: Organization ID for access control
            max_results: Maximum number of results to return

        Returns:
            List of search results
        """
        try:
            # Validate required fields
            if "filters" not in metadata_query or "nodes" not in metadata_query["filters"]:
                logger.error("Missing required filters or nodes in metadata query")
                return []

            # Extract nodes from the filters
            nodes = metadata_query["filters"]["nodes"]

            if not nodes:
                logger.error("No nodes specified in the metadata query")
                return []

            # Generate separate queries for each node type
            all_results = []

            for i, node in enumerate(nodes):
                try:
                    logger.info(f"Processing node {i+1}/{len(nodes)}: {node.get('node_type', 'Unknown')}")

                    # Build and execute query for this specific node
                    cypher_query, params = self._build_single_node_query(node, organisation_id, max_results)

                    logger.info(f"Generated query for {node.get('node_type', 'Unknown')}:")
                    logger.info(f"Query: {cypher_query}")
                    logger.info(f"Params: {params}")

                    # Execute the query
                    results = execute_read_query(cypher_query, params)

                    # Process results for this node
                    for record in results:
                        processed_result = self._process_single_node_record(record, node)
                        if processed_result:
                            all_results.append(processed_result)

                except Exception as e:
                    logger.error(f"Error processing node {i+1}: {str(e)}")
                    continue

            logger.info(f"Total results from all nodes: {len(all_results)}")
            return all_results

        except Exception as e:
            logger.error(f"Error in search_by_metadata_query: {str(e)}")
            return []

    def _build_single_node_query(self, node: Dict[str, Any], organisation_id: str, max_results: int) -> Tuple[str, Dict[str, Any]]:
        """
        Build a Cypher query for a single node.

        Args:
            node: The node configuration
            organisation_id: Organization ID for access control
            max_results: Maximum number of results to return

        Returns:
            Tuple of (cypher_query, params)
        """
        node_type = node.get("node_type", "Issue")
        node_filters = node.get("filters", [])
        properties_to_return = node.get("properties_to_return", ["*"])

        # Build the MATCH clause
        cypher_query = f"MATCH (node:{node_type})\n"

        # Build the WHERE clause
        where_clauses = []

        # Add organization filter based on node type
        if node_type == "Project":
            where_clauses.append("node.org_id = $organisation_id")
        else:
            where_clauses.append("node.organisation_id = $organisation_id")

        # Initialize parameters
        params = {
            "organisation_id": organisation_id,
            "limit": max_results
        }

        # Process filters
        for i, filter_item in enumerate(node_filters):
            property_name = filter_item.get("property_name")
            condition = filter_item.get("condition", {})
            operator = condition.get("operator")
            value = condition.get("value")
            value2 = condition.get("value2")

            if not property_name or not operator:
                continue

            # Handle different operators
            param_name = f"param_{i}"

            # For string properties, make the comparison case-insensitive
            if isinstance(value, str) and property_name in ["name", "key", "status", "priority", "issue_type", "state"]:
                if operator == "=":
                    where_clauses.append(f"toLower(node.{property_name}) = toLower(${param_name})")
                    params[param_name] = value
                elif operator == "!=":
                    where_clauses.append(f"toLower(node.{property_name}) <> toLower(${param_name})")
                    params[param_name] = value
                elif operator == "CONTAINS":
                    where_clauses.append(f"toLower(node.{property_name}) CONTAINS toLower(${param_name})")
                    params[param_name] = value
                elif operator == "STARTS_WITH":
                    where_clauses.append(f"toLower(node.{property_name}) STARTS WITH toLower(${param_name})")
                    params[param_name] = value
                elif operator == "ENDS_WITH":
                    where_clauses.append(f"toLower(node.{property_name}) ENDS WITH toLower(${param_name})")
                    params[param_name] = value
                elif operator == "IN":
                    where_clauses.append(f"toLower(node.{property_name}) IN ${param_name}")
                    if isinstance(value, str) and "," in value:
                        params[param_name] = [v.strip().lower() for v in value.split(",")]
                    elif isinstance(value, list):
                        params[param_name] = [v.lower() for v in value if isinstance(v, str)]
                    else:
                        params[param_name] = [value.lower()] if isinstance(value, str) else [value]
                else:
                    # For other operators, use standard comparison
                    where_clauses.append(f"node.{property_name} {self._get_operator_symbol(operator)} ${param_name}")
                    params[param_name] = value
            else:
                # For non-string properties or non-text fields, use standard comparison
                if operator == "=":
                    where_clauses.append(f"node.{property_name} = ${param_name}")
                    params[param_name] = value
                elif operator == ">":
                    where_clauses.append(f"node.{property_name} > ${param_name}")
                    params[param_name] = value
                elif operator == ">=":
                    where_clauses.append(f"node.{property_name} >= ${param_name}")
                    params[param_name] = value
                elif operator == "<":
                    where_clauses.append(f"node.{property_name} < ${param_name}")
                    params[param_name] = value
                elif operator == "<=":
                    where_clauses.append(f"node.{property_name} <= ${param_name}")
                    params[param_name] = value
                elif operator == "!=":
                    where_clauses.append(f"node.{property_name} <> ${param_name}")
                    params[param_name] = value
                elif operator == "CONTAINS":
                    where_clauses.append(f"node.{property_name} CONTAINS ${param_name}")
                    params[param_name] = value
                elif operator == "STARTS_WITH":
                    where_clauses.append(f"node.{property_name} STARTS WITH ${param_name}")
                    params[param_name] = value
                elif operator == "ENDS_WITH":
                    where_clauses.append(f"node.{property_name} ENDS WITH ${param_name}")
                    params[param_name] = value
                elif operator == "IN":
                    where_clauses.append(f"node.{property_name} IN ${param_name}")
                    if isinstance(value, str) and "," in value:
                        params[param_name] = [v.strip() for v in value.split(",")]
                    elif isinstance(value, list):
                        params[param_name] = value
                    else:
                        params[param_name] = [value]
                elif operator == "BETWEEN" and value2 is not None:
                    where_clauses.append(f"node.{property_name} >= ${param_name}_1 AND node.{property_name} <= ${param_name}_2")
                    params[f"{param_name}_1"] = value
                    params[f"{param_name}_2"] = value2

        # Add WHERE clause if there are any conditions
        if where_clauses:
            cypher_query += "WHERE " + " AND ".join(where_clauses) + "\n"

        # Build the RETURN clause
        if properties_to_return == ["*"]:
            cypher_query += "RETURN node\n"
        else:
            return_clauses = [f"node.{prop} AS {prop}" for prop in properties_to_return]
            cypher_query += "RETURN node, " + ", ".join(return_clauses) + "\n"

        # Add LIMIT clause
        cypher_query += "LIMIT $limit"

        return cypher_query, params

    def _process_single_node_record(self, record: Dict[str, Any], node_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a Neo4j record for a single node into a structured result.

        Args:
            record: Neo4j record
            node_config: Node configuration from the query

        Returns:
            Processed result dictionary
        """
        node = record.get("node", {})
        if not node:
            return None

        node_type = node_config.get("node_type", "Unknown")
        properties_to_return = node_config.get("properties_to_return", ["*"])

        # Base result structure
        result = {
            "search_type": "metadata",
            "node_type": node_type
        }

        # Add requested properties or all properties
        if properties_to_return == ["*"]:
            # Add all node properties except internal ones
            for key, value in node.items():
                if key not in ["organisation_id", "org_id"]:
                    result[key] = value
        else:
            # Add only requested properties
            for prop in properties_to_return:
                if prop in node:
                    result[prop] = node[prop]
                elif prop in record:  # Check if it's in the record as an alias
                    result[prop] = record[prop]

        return result

    def _build_graph_traversal_query(self, metadata_query: Dict[str, Any], organisation_id: str, max_results: int) -> Tuple[str, Dict[str, Any]]:
        """
        Build a Cypher query for graph traversal search strategy.
        
        Args:
            metadata_query: The structured metadata query
            organisation_id: Organization ID for access control
            max_results: Maximum number of results to return
            
        Returns:
            Tuple of (cypher_query, params)
        """
        # Extract nodes and their filters
        nodes = metadata_query["filters"]["nodes"]
        
        if not nodes:
            raise ValueError("No nodes specified in the query")
            
        # For simple queries with a single node, use the original approach
        if len(nodes) == 1:
            return self._build_simple_graph_traversal_query(nodes[0], organisation_id, max_results)
        
        # For complex queries with multiple nodes, build a more complex query
        return self._build_complex_graph_traversal_query(nodes, organisation_id, max_results)
    
    def _build_simple_graph_traversal_query(self, node: Dict[str, Any], organisation_id: str, max_results: int) -> Tuple[str, Dict[str, Any]]:
        """
        Build a Cypher query for a simple graph traversal with a single node.
        
        Args:
            node: The node configuration
            organisation_id: Organization ID for access control
            max_results: Maximum number of results to return
            
        Returns:
            Tuple of (cypher_query, params)
        """
        node_type = node.get("node_type", "Issue")
        node_filters = node.get("filters", [])
        properties_to_return = node.get("properties_to_return", ["*"])
        
        # Build the MATCH clause
        cypher_query = f"MATCH (node:{node_type})\n"
        
        # Build the WHERE clause
        where_clauses = ["node.organisation_id = $organisation_id"]
        
        # Initialize parameters
        params = {
            "organisation_id": organisation_id,
            "limit": max_results
        }
        
        # Process filters
        for i, filter_item in enumerate(node_filters):
            property_name = filter_item.get("property_name")
            condition = filter_item.get("condition", {})
            operator = condition.get("operator")
            value = condition.get("value")
            value2 = condition.get("value2")
            
            if not property_name or not operator:
                continue
                
            # Handle different operators
            param_name = f"param_{i}"
            
            if operator == "=":
                where_clauses.append(f"node.{property_name} = ${param_name}")
                params[param_name] = value
            elif operator == ">":
                where_clauses.append(f"node.{property_name} > ${param_name}")
                params[param_name] = value
            elif operator == ">=":
                where_clauses.append(f"node.{property_name} >= ${param_name}")
                params[param_name] = value
            elif operator == "<":
                where_clauses.append(f"node.{property_name} < ${param_name}")
                params[param_name] = value
            elif operator == "<=":
                where_clauses.append(f"node.{property_name} <= ${param_name}")
                params[param_name] = value
            elif operator == "!=":
                where_clauses.append(f"node.{property_name} <> ${param_name}")
                params[param_name] = value
            elif operator == "CONTAINS":
                where_clauses.append(f"node.{property_name} CONTAINS ${param_name}")
                params[param_name] = value
            elif operator == "STARTS_WITH":
                where_clauses.append(f"node.{property_name} STARTS WITH ${param_name}")
                params[param_name] = value
            elif operator == "ENDS_WITH":
                where_clauses.append(f"node.{property_name} ENDS WITH ${param_name}")
                params[param_name] = value
            elif operator == "IN":
                where_clauses.append(f"node.{property_name} IN ${param_name}")
                params[param_name] = value if isinstance(value, list) else [value]
            elif operator == "BETWEEN" and value2 is not None:
                where_clauses.append(f"node.{property_name} >= ${param_name}_1 AND node.{property_name} <= ${param_name}_2")
                params[f"{param_name}_1"] = value
                params[f"{param_name}_2"] = value2
                
        # Add WHERE clause if there are any conditions
        if where_clauses:
            cypher_query += "WHERE " + " AND ".join(where_clauses) + "\n"
            
        # Build the RETURN clause
        if properties_to_return == ["*"]:
            cypher_query += "RETURN node\n"
        else:
            return_clauses = [f"node.{prop} AS {prop}" for prop in properties_to_return]
            cypher_query += "RETURN node, " + ", ".join(return_clauses) + "\n"
            
        # Add LIMIT clause
        cypher_query += "LIMIT $limit"
        
        return cypher_query, params
    
    def _build_complex_graph_traversal_query(self, nodes: List[Dict[str, Any]], organisation_id: str, max_results: int) -> Tuple[str, Dict[str, Any]]:
        """
        Build a Cypher query for complex graph traversal with multiple nodes.
        
        Args:
            nodes: List of node configurations
            organisation_id: Organization ID for access control
            max_results: Maximum number of results to return
            
        Returns:
            Tuple of (cypher_query, params)
        """
        # Initialize parameters
        params = {
            "organisation_id": organisation_id,
            "limit": max_results
        }
        
        # Build the MATCH clauses for each node
        match_clauses = []
        where_clauses = []
        return_items = []
        
        for i, node in enumerate(nodes):
            node_type = node.get("node_type", "Issue")
            node_var = f"n{i}"
            node_filters = node.get("filters", [])
            properties_to_return = node.get("properties_to_return", ["*"])
            
            # Add node to MATCH clause
            match_clauses.append(f"({node_var}:{node_type})")
            
            # Add organisation_id filter
            where_clauses.append(f"{node_var}.organisation_id = $organisation_id")
            
            # Process node filters
            for j, filter_item in enumerate(node_filters):
                property_name = filter_item.get("property_name")
                condition = filter_item.get("condition", {})
                operator = condition.get("operator")
                value = condition.get("value")
                value2 = condition.get("value2")
                
                if not property_name or not operator:
                    continue
                    
                # Handle different operators
                param_name = f"param_{i}_{j}"
                
                # For string properties, make the comparison case-insensitive
                if isinstance(value, str) and property_name in ["name", "key", "status", "priority", "issue_type", "state"]:
                    if operator == "=":
                        where_clauses.append(f"toLower({node_var}.{property_name}) = toLower(${param_name})")
                        params[param_name] = value
                    elif operator == "!=":
                        where_clauses.append(f"toLower({node_var}.{property_name}) <> toLower(${param_name})")
                        params[param_name] = value
                    elif operator == "CONTAINS":
                        where_clauses.append(f"toLower({node_var}.{property_name}) CONTAINS toLower(${param_name})")
                        params[param_name] = value
                    elif operator == "STARTS_WITH":
                        where_clauses.append(f"toLower({node_var}.{property_name}) STARTS WITH toLower(${param_name})")
                        params[param_name] = value
                    elif operator == "ENDS_WITH":
                        where_clauses.append(f"toLower({node_var}.{property_name}) ENDS WITH toLower(${param_name})")
                        params[param_name] = value
                    elif operator == "IN":
                        # For IN operator with strings, convert each value to lowercase
                        where_clauses.append(f"toLower({node_var}.{property_name}) IN ${param_name}")
                        if isinstance(value, str) and "," in value:
                            params[param_name] = [v.lower() for v in value.split(",")]
                        elif isinstance(value, list):
                            params[param_name] = [v.lower() for v in value if isinstance(v, str)]
                        else:
                            params[param_name] = value.lower() if isinstance(value, str) else value
                    else:
                        # For other operators, use standard comparison
                        where_clauses.append(f"{node_var}.{property_name} {self._get_operator_symbol(operator)} ${param_name}")
                        params[param_name] = value
                else:
                    # For non-string properties or non-text fields, use standard comparison
                    if operator == "=":
                        where_clauses.append(f"{node_var}.{property_name} = ${param_name}")
                        params[param_name] = value
                    elif operator == ">":
                        where_clauses.append(f"{node_var}.{property_name} > ${param_name}")
                        params[param_name] = value
                    elif operator == ">=":
                        where_clauses.append(f"{node_var}.{property_name} >= ${param_name}")
                        params[param_name] = value
                    elif operator == "<":
                        where_clauses.append(f"{node_var}.{property_name} < ${param_name}")
                        params[param_name] = value
                    elif operator == "<=":
                        where_clauses.append(f"{node_var}.{property_name} <= ${param_name}")
                        params[param_name] = value
                    elif operator == "!=":
                        where_clauses.append(f"{node_var}.{property_name} <> ${param_name}")
                        params[param_name] = value
                    elif operator == "CONTAINS":
                        where_clauses.append(f"{node_var}.{property_name} CONTAINS ${param_name}")
                        params[param_name] = value
                    elif operator == "STARTS_WITH":
                        where_clauses.append(f"{node_var}.{property_name} STARTS WITH ${param_name}")
                        params[param_name] = value
                    elif operator == "ENDS_WITH":
                        where_clauses.append(f"{node_var}.{property_name} ENDS WITH ${param_name}")
                        params[param_name] = value
                    elif operator == "IN":
                        where_clauses.append(f"{node_var}.{property_name} IN ${param_name}")
                        params[param_name] = value.split(",") if isinstance(value, str) and "," in value else value
                    elif operator == "BETWEEN" and value2 is not None:
                        where_clauses.append(f"{node_var}.{property_name} >= ${param_name}_1 AND {node_var}.{property_name} <= ${param_name}_2")
                        params[f"{param_name}_1"] = value
                        params[f"{param_name}_2"] = value2
            
            # Add node to RETURN items
            return_items.append(node_var)
            
            # Add specific properties to RETURN if specified
            if properties_to_return != ["*"]:
                for prop in properties_to_return:
                    return_items.append(f"{node_var}.{prop} AS {node_type.lower()}_{prop}")
        
        # Build relationships between nodes based on common patterns
        relationship_clauses = []
        
        # Check for common node type combinations and add appropriate relationships
        node_types = [node.get("node_type") for node in nodes]
        
        # Issue -> User relationship (ASSIGNED_TO)
        if ("User" in node_types or "JiraUser" in node_types) and "Issue" in node_types:
            user_idx = node_types.index("User") if "User" in node_types else node_types.index("JiraUser")
            issue_idx = node_types.index("Issue")
            relationship_clauses.append(f"({f'n{issue_idx}'})-[:ASSIGNED_TO]->({f'n{user_idx}'})")
        
        # Project -> Issue relationship (HAS_ISSUE)
        if "Project" in node_types and "Issue" in node_types:
            project_idx = node_types.index("Project")
            issue_idx = node_types.index("Issue")
            relationship_clauses.append(f"({f'n{project_idx}'})-[:HAS_ISSUE]->({f'n{issue_idx}'})")
        
        # Issue -> Sprint relationship (IN_SPRINT)
        if "Issue" in node_types and "Sprint" in node_types:
            issue_idx = node_types.index("Issue")
            sprint_idx = node_types.index("Sprint")
            relationship_clauses.append(f"({f'n{issue_idx}'})-[:IN_SPRINT]->({f'n{sprint_idx}'})")
        
        # Issue -> Issue parent relationship (HAS_PARENT)
        if "Issue" in node_types and node_types.count("Issue") > 1:
            issue_indices = [i for i, nt in enumerate(node_types) if nt == "Issue"]
            if len(issue_indices) >= 2:
                relationship_clauses.append(f"({f'n{issue_indices[0]}'})-[:HAS_PARENT]->({f'n{issue_indices[1]}'})")
        
        # Build the final Cypher query
        cypher_query = "MATCH " + ", ".join(match_clauses) + "\n"
        
        # Add relationship clauses if any
        if relationship_clauses:
            cypher_query += "MATCH " + ", ".join(relationship_clauses) + "\n"
        
        # Add WHERE clause if there are any conditions
        if where_clauses:
            cypher_query += "WHERE " + " AND ".join(where_clauses) + "\n"
        
        # Add RETURN clause
        cypher_query += "RETURN " + ", ".join(return_items) + "\n"
        
        # Add LIMIT clause
        cypher_query += "LIMIT $limit"
        
        return cypher_query, params
    
    def _get_operator_symbol(self, operator: str) -> str:
        """
        Convert operator string to Cypher operator symbol.
        
        Args:
            operator: Operator string
            
        Returns:
            Cypher operator symbol
        """
        operator_map = {
            "=": "=",
            ">": ">",
            ">=": ">=",
            "<": "<",
            "<=": "<=",
            "!=": "<>",
            "CONTAINS": "CONTAINS",
            "STARTS_WITH": "STARTS WITH",
            "ENDS_WITH": "ENDS WITH",
            "IN": "IN"
        }
        return operator_map.get(operator, "=")
        
    def _build_aggregation_query(self, metadata_query: Dict[str, Any], organisation_id: str, max_results: int) -> Tuple[str, Dict[str, Any]]:
        """
        Build a Cypher query for aggregation search strategy.
        
        Args:
            metadata_query: The structured metadata query
            organisation_id: Organization ID for access control
            max_results: Maximum number of results to return
            
        Returns:
            Tuple of (cypher_query, params)
        """
        # This is a placeholder for the aggregation query builder
        # Implement based on specific aggregation requirements
        return self._build_graph_traversal_query(metadata_query, organisation_id, max_results)
        
    def _build_path_finding_query(self, metadata_query: Dict[str, Any], organisation_id: str, max_results: int) -> Tuple[str, Dict[str, Any]]:
        """
        Build a Cypher query for path finding search strategy.
        
        Args:
            metadata_query: The structured metadata query
            organisation_id: Organization ID for access control
            max_results: Maximum number of results to return
            
        Returns:
            Tuple of (cypher_query, params)
        """
        # This is a placeholder for the path finding query builder
        # Implement based on specific path finding requirements
        return self._build_graph_traversal_query(metadata_query, organisation_id, max_results)
        
    def _process_node_record(self, node: Dict[str, Any], record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a Neo4j record into a structured result based on node type.
        
        Args:
            node: Node properties
            record: Full Neo4j record
            
        Returns:
            Processed result dictionary
        """
        if not node:
            return None
            
        # Determine node type and process accordingly
        if "issue_type" in node:
            # It's an Issue node
            return self._process_issue_record(
                node,
                record.get("assignee", {}),
                record.get("reporter", {}),
                record.get("project", {}),
                record.get("sprint", {}),
                record.get("parent", {})
            )
        elif "name" in node and "key" in node:
            # It's a Project node
            return {
                "id": node.get("id", ""),
                "key": node.get("key", ""),
                "name": node.get("name", ""),
                "search_type": "metadata",
                "node_type": "Project"
            }
        elif "name" in node and "state" in node:
            # It's a Sprint node
            return {
                "id": node.get("id", ""),
                "name": node.get("name", ""),
                "state": node.get("state", ""),
                "start_date": node.get("start_date", ""),
                "end_date": node.get("end_date", ""),
                "search_type": "metadata",
                "node_type": "Sprint"
            }
        else:
            # Generic node processing
            result = {
                "search_type": "metadata",
                "node_type": "Unknown"
            }
            
            # Add all node properties
            for key, value in node.items():
                if key not in ["organisation_id", "org_id"]:
                    result[key] = value
                    
            return result
    ########################################################################

    def _search_by_issue_key(self, issue_key: str, organisation_id: str, max_results: int) -> List[Dict[str, Any]]:
        """
        Search for a specific issue by key.
        
        Args:
            issue_key: The key of the Jira issue
            organisation_id: Organization ID
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Build Cypher query for issue search
            cypher_query = """
            MATCH (i:Issue {key: $issue_key, organisation_id: $organisation_id})
            OPTIONAL MATCH (i)-[:ASSIGNED_TO]->(assignee:User)
            OPTIONAL MATCH (i)-[:REPORTED_TO]->(reporter:User)
            OPTIONAL MATCH (p:Project)-[:HAS_ISSUE]->(i)
            OPTIONAL MATCH (i)-[:IN_SPRINT]->(s:Sprint)
            OPTIONAL MATCH (i)-[:HAS_PARENT]->(parent:Issue)
            RETURN i, 
                   assignee, 
                   reporter, 
                   p as project,
                   s as sprint,
                   parent
            LIMIT $limit
            """
            
            params = {
                "issue_key": issue_key,
                "organisation_id": organisation_id,
                "limit": max_results
            }
            
            results = execute_read_query(cypher_query, params)
            
            # Process results
            processed_results = []
            
            for record in results:
                issue = record.get("i", {})
                assignee = record.get("assignee", {})
                reporter = record.get("reporter", {})
                project = record.get("project", {})
                sprint = record.get("sprint", {})
                parent = record.get("parent", {})
                
                processed_result = self._process_issue_record(
                    issue, assignee, reporter, project, sprint, parent
                )
                
                if processed_result:
                    processed_results.append(processed_result)
            
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in search_by_issue_key: {str(e)}")
            return []
    
    def _search_by_project(self, project_key: str, organisation_id: str, 
                         status: Optional[str] = None, priority: Optional[str] = None,
                         max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search for issues in a specific project.
        
        Args:
            project_key: The key of the Jira project
            organisation_id: Organization ID
            status: Optional status filter
            priority: Optional priority filter
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Build Cypher query for project search
            cypher_query = """
            MATCH (p:Project {key: $project_key, org_id: $organisation_id})-[:HAS_ISSUE]->(i:Issue)
            WHERE i.organisation_id = $organisation_id
            """
            
            params = {
                "project_key": project_key,
                "organisation_id": organisation_id,
                "limit": max_results
            }
            
            # Add status filter if provided
            if status:
                cypher_query += " AND i.status = $status"
                params["status"] = status
            
            # Add priority filter if provided
            if priority:
                cypher_query += " AND i.priority = $priority"
                params["priority"] = priority
            
            cypher_query += """
            OPTIONAL MATCH (i)-[:ASSIGNED_TO]->(assignee:User)
            OPTIONAL MATCH (i)-[:REPORTED_TO]->(reporter:User)
            OPTIONAL MATCH (i)-[:IN_SPRINT]->(s:Sprint)
            OPTIONAL MATCH (i)-[:HAS_PARENT]->(parent:Issue)
            RETURN i, 
                   assignee, 
                   reporter, 
                   p as project,
                   s as sprint,
                   parent
            ORDER BY i.updated_at DESC
            LIMIT $limit
            """
            
            results = execute_read_query(cypher_query, params)
            
            # Process results
            processed_results = []
            
            for record in results:
                issue = record.get("i", {})
                assignee = record.get("assignee", {})
                reporter = record.get("reporter", {})
                project = record.get("project", {})
                sprint = record.get("sprint", {})
                parent = record.get("parent", {})
                
                processed_result = self._process_issue_record(
                    issue, assignee, reporter, project, sprint, parent
                )
                
                if processed_result:
                    processed_results.append(processed_result)
            
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in search_by_project: {str(e)}")
            return []
    
    def _search_by_sprint(self, project_key: str, sprint_name: str, 
                        organisation_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search for issues in a specific sprint.
        
        Args:
            project_key: The key of the Jira project
            sprint_name: The name of the sprint
            organisation_id: Organization ID
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Build Cypher query for sprint search
            cypher_query = """
            MATCH (p:Project {key: $project_key, org_id: $organisation_id})-[:HAS_SPRINT]->(s:Sprint)
            WHERE s.org_id = $organisation_id AND toLower(s.name) CONTAINS toLower($sprint_name)
            MATCH (i:Issue)-[:IN_SPRINT]->(s)
            WHERE i.organisation_id = $organisation_id
            OPTIONAL MATCH (i)-[:ASSIGNED_TO]->(assignee:User)
            OPTIONAL MATCH (i)-[:REPORTED_TO]->(reporter:User)
            OPTIONAL MATCH (i)-[:HAS_PARENT]->(parent:Issue)
            RETURN i, 
                   assignee, 
                   reporter, 
                   p as project,
                   s as sprint,
                   parent
            ORDER BY i.updated_at DESC
            LIMIT $limit
            """
            
            params = {
                "project_key": project_key,
                "sprint_name": sprint_name,
                "organisation_id": organisation_id,
                "limit": max_results
            }
            
            results = execute_read_query(cypher_query, params)
            
            # Process results
            processed_results = []
            
            for record in results:
                issue = record.get("i", {})
                assignee = record.get("assignee", {})
                reporter = record.get("reporter", {})
                project = record.get("project", {})
                sprint = record.get("sprint", {})
                parent = record.get("parent", {})
                
                processed_result = self._process_issue_record(
                    issue, assignee, reporter, project, sprint, parent
                )
                
                if processed_result:
                    processed_results.append(processed_result)
            
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in search_by_sprint: {str(e)}")
            return []
    
    def _search_by_user(self, user_email: str, organisation_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search for issues assigned to or reported by a specific user.
        
        Args:
            user_email: The email of the Jira user
            organisation_id: Organization ID
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Build Cypher query for user search
            cypher_query = """
            MATCH (u:User {email: $user_email, organisation_id: $organisation_id})
            MATCH (i:Issue)
            WHERE i.organisation_id = $organisation_id
            AND ((u)-[:ASSIGNED_TO]->(i) OR (u)-[:REPORTED_TO]->(i))
            OPTIONAL MATCH (i)-[:ASSIGNED_TO]->(assignee:User)
            OPTIONAL MATCH (i)-[:REPORTED_TO]->(reporter:User)
            OPTIONAL MATCH (p:Project)-[:HAS_ISSUE]->(i)
            OPTIONAL MATCH (i)-[:IN_SPRINT]->(s:Sprint)
            OPTIONAL MATCH (i)-[:HAS_PARENT]->(parent:Issue)
            RETURN i, 
                   assignee, 
                   reporter, 
                   p as project,
                   s as sprint,
                   parent
            ORDER BY i.updated_at DESC
            LIMIT $limit
            """
            
            params = {
                "user_email": user_email,
                "organisation_id": organisation_id,
                "limit": max_results
            }
            
            results = execute_read_query(cypher_query, params)
            
            # Process results
            processed_results = []
            
            for record in results:
                issue = record.get("i", {})
                assignee = record.get("assignee", {})
                reporter = record.get("reporter", {})
                project = record.get("project", {})
                sprint = record.get("sprint", {})
                parent = record.get("parent", {})
                
                processed_result = self._process_issue_record(
                    issue, assignee, reporter, project, sprint, parent
                )
                
                if processed_result:
                    processed_results.append(processed_result)
            
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in search_by_user: {str(e)}")
            return []
    
    def _search_by_criteria(self, query_plan: Dict[str, Any], max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search for issues based on multiple criteria.
        
        Args:
            query_plan: The query plan with search criteria
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Extract parameters
            organisation_id = query_plan.get("organisation_id")
            status = query_plan.get("status")
            priority = query_plan.get("priority")
            start_date = query_plan.get("start_date")
            end_date = query_plan.get("end_date")
            
            # Build Cypher query for criteria search
            cypher_query = """
            MATCH (i:Issue)
            WHERE i.organisation_id = $organisation_id
            """
            
            params = {
                "organisation_id": organisation_id,
                "limit": max_results
            }
            
            # Add status filter if provided
            if status:
                cypher_query += " AND i.status = $status"
                params["status"] = status
            
            # Add priority filter if provided
            if priority:
                cypher_query += " AND i.priority = $priority"
                params["priority"] = priority
            
            # Add date range filters if provided
            if start_date:
                cypher_query += " AND i.created_at >= datetime($start_date)"
                params["start_date"] = start_date
            
            if end_date:
                cypher_query += " AND i.created_at <= datetime($end_date)"
                params["end_date"] = end_date
            
            cypher_query += """
            OPTIONAL MATCH (i)-[:ASSIGNED_TO]->(assignee:User)
            OPTIONAL MATCH (i)-[:REPORTED_TO]->(reporter:User)
            OPTIONAL MATCH (p:Project)-[:HAS_ISSUE]->(i)
            OPTIONAL MATCH (i)-[:IN_SPRINT]->(s:Sprint)
            OPTIONAL MATCH (i)-[:HAS_PARENT]->(parent:Issue)
            RETURN i, 
                   assignee, 
                   reporter, 
                   p as project,
                   s as sprint,
                   parent
            ORDER BY i.updated_at DESC
            LIMIT $limit
            """
            
            results = execute_read_query(cypher_query, params)
            
            # Process results
            processed_results = []
            
            for record in results:
                issue = record.get("i", {})
                assignee = record.get("assignee", {})
                reporter = record.get("reporter", {})
                project = record.get("project", {})
                sprint = record.get("sprint", {})
                parent = record.get("parent", {})
                
                processed_result = self._process_issue_record(
                    issue, assignee, reporter, project, sprint, parent
                )
                
                if processed_result:
                    processed_results.append(processed_result)
            
            return processed_results
            
        except Exception as e:
            logger.error(f"Error in search_by_criteria: {str(e)}")
            return []
    
    def _process_issue_record(self, issue: Dict[str, Any], assignee: Dict[str, Any], 
                            reporter: Dict[str, Any], project: Dict[str, Any],
                            sprint: Dict[str, Any], parent: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a Neo4j record into a structured result.
        
        Args:
            issue: Issue node properties
            assignee: Assignee node properties
            reporter: Reporter node properties
            project: Project node properties
            sprint: Sprint node properties
            parent: Parent issue node properties
            
        Returns:
            Processed result dictionary
        """
        if not issue:
            return None
        
        # Extract issue properties
        result = {
            "id": issue.get("id", ""),
            "key": issue.get("key", ""),
            "title": issue.get("title", ""),
            "description": issue.get("description", ""),
            "status": issue.get("status", ""),
            "priority": issue.get("priority", ""),
            "issue_type": issue.get("issue_type", ""),
            "created_at": issue.get("created_at", ""),
            "updated_at": issue.get("updated_at", ""),
            "search_type": "metadata"
        }
        
        # Add assignee information
        if assignee:
            result["assignee"] = {
                "id": assignee.get("id", ""),
                "name": assignee.get("name", ""),
                "email": assignee.get("email", "")
            }
        else:
            result["assignee"] = None
        
        # Add reporter information
        if reporter:
            result["reporter"] = {
                "id": reporter.get("id", ""),
                "name": reporter.get("name", ""),
                "email": reporter.get("email", "")
            }
        else:
            result["reporter"] = None
        
        # Add project information
        if project:
            result["project"] = {
                "key": project.get("key", ""),
                "name": project.get("name", "")
            }
        else:
            result["project"] = None
        
        # Add sprint information
        if sprint:
            result["sprint"] = {
                "id": sprint.get("id", ""),
                "name": sprint.get("name", ""),
                "state": sprint.get("state", ""),
                "start_date": sprint.get("start_date", ""),
                "end_date": sprint.get("end_date", "")
            }
        else:
            result["sprint"] = None
        
        # Add parent issue information
        if parent:
            result["parent"] = {
                "id": parent.get("id", ""),
                "key": parent.get("key", ""),
                "title": parent.get("title", "")
            }
        else:
            result["parent"] = None
        
        return result