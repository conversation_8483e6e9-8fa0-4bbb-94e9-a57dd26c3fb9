"""
GitHub Advanced Search Service

Implements comprehensive search capabilities for GitHub data with multiple strategies.
Supports searching across repositories, pull requests, commits, branches, users, teams, and more.
"""

import logging
import asyncio
import hashlib
import json
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

from app.services.neo4j_service import execute_read_query, execute_write_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService

logger = logging.getLogger(__name__)


class GitHubSearchType(Enum):
    """GitHub entity search types"""
    REPOSITORY = "repository"
    PULL_REQUEST = "pull_request"
    ISSUE = "issue"
    COMMIT = "commit"
    BRANCH = "branch"
    USER = "user"
    TEAM = "team"
    ORGANIZATION = "organization"
    FILE = "file"
    CODE = "code"
    ALL = "all"


class SearchStrategy(Enum):
    """Search strategy types"""
    GRAPH_TRAVERSAL = "graph_traversal"
    SEMANTIC_ONLY = "semantic_only"
    ENTITY_CENTRIC = "entity_centric"
    RELATIONSHIP_CENTRIC = "relationship_centric"
    HYBRID = "hybrid"


class GitHubSearchService:
    """Advanced GitHub search service with multiple strategies"""
    
    def __init__(self, pinecone_service: PineconeService = None, redis_service: RedisService = None):
        self.pinecone_service = pinecone_service
        self.redis_service = redis_service
        self.cache_ttl = 300  # 5 minutes
        
        # GitHub entity type mappings
        self.entity_types = {
            'repository': 'GitHubRepository',
            'pull_request': 'GitHubPullRequest',
            'issue': 'GitHubIssue', 
            'commit': 'GitHubCommit',
            'branch': 'GitHubBranch',
            'user': 'GitHubUser',
            'team': 'GitHubTeam',
            'organization': 'GitHubOrganization',
            'file': 'GitHubFile',
            'release': 'GitHubRelease',
            'milestone': 'GitHubMilestone'
        }
        
        # Relationship mappings for graph traversal
        self.relationship_types = {
            'owns': 'OWNS_REPOSITORY',
            'contributes': 'CONTRIBUTES_TO',
            'assigned': 'ASSIGNED_TO',
            'authored': 'AUTHORED_BY',
            'reviewed': 'REVIEWED_BY',
            'member_of': 'MEMBER_OF',
            'belongs_to': 'BELONGS_TO',
            'forked_from': 'FORKED_FROM',
            'merged_into': 'MERGED_INTO',
            'created_by': 'CREATED_BY',
            'has_branch': 'HAS_BRANCH',
            'has_commit': 'HAS_COMMIT',
            'has_file': 'HAS_FILE',
            'tagged_in': 'TAGGED_IN'
        }

    async def search(self, query: str, organisation_id: str, user_id: str = None,
                    search_type: GitHubSearchType = GitHubSearchType.ALL, 
                    strategy: SearchStrategy = SearchStrategy.HYBRID, 
                    limit: int = 20, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main search entry point with intelligent strategy selection
        
        Args:
            query: Search query string
            organisation_id: Organisation ID
            user_id: Optional user ID for user-specific searches
            search_type: Type of GitHub entity to search
            strategy: Search strategy to use
            limit: Maximum number of results
            filters: Additional search filters
            
        Returns:
            Search results with metadata
        """
        try:
            # Cache key for results
            cache_key = self._generate_cache_key(query, organisation_id, user_id, search_type, strategy, filters)
            
            # Check cache first
            if self.redis_service:
                cached_result = self.redis_service.get(cache_key)
                if cached_result:
                    return json.loads(cached_result)
            
            # Determine optimal search strategy if not specified
            if strategy == SearchStrategy.HYBRID:
                strategy = await self._determine_optimal_strategy(query, search_type, filters)
            
            # Execute search based on strategy
            if strategy == SearchStrategy.GRAPH_TRAVERSAL:
                result = await self._graph_traversal_search(query, organisation_id, user_id, search_type, filters, limit)
            elif strategy == SearchStrategy.SEMANTIC_ONLY:
                result = await self._vector_search(query, organisation_id, user_id, search_type, filters, limit)
            elif strategy == SearchStrategy.ENTITY_CENTRIC:
                result = await self._entity_centric_search(query, organisation_id, user_id, search_type, filters, limit)
            elif strategy == SearchStrategy.RELATIONSHIP_CENTRIC:
                result = await self._relationship_centric_search(query, organisation_id, user_id, search_type, filters, limit)
            else:
                result = await self._hybrid_search(query, organisation_id, user_id, search_type, filters, limit)
            
            # Cache result
            if self.redis_service and result.get('success', False):
                self.redis_service.setex(cache_key, self.cache_ttl, json.dumps(result))
            
            return result
            
        except Exception as e:
            logger.error(f"Error in GitHub search: {str(e)}")
            return await self._fallback_search(query, organisation_id, user_id, limit)

    async def _determine_optimal_strategy(self, query: str, search_type: GitHubSearchType, 
                                        filters: Dict[str, Any]) -> SearchStrategy:
        """Intelligently determine the best search strategy"""
        
        # Analyze query characteristics
        has_structured_terms = any(term in query.lower() for term in 
                                 ['repository:', 'author:', 'assignee:', 'label:', 'milestone:', 'branch:', 'commit:'])
        has_semantic_terms = len(query.split()) > 3 and not has_structured_terms
        has_entity_focus = any(entity in query.lower() for entity in 
                             ['user', 'repo', 'organization', 'team', 'branch', 'commit'])
        has_relationship_focus = any(rel in query.lower() for rel in 
                                   ['assigned to', 'authored by', 'reviewed by', 'member of', 'owns', 'forked from'])
        
        # Strategy selection logic
        if has_structured_terms or search_type in [GitHubSearchType.REPOSITORY, GitHubSearchType.USER, GitHubSearchType.BRANCH]:
            return SearchStrategy.GRAPH_TRAVERSAL
        elif has_relationship_focus:
            return SearchStrategy.RELATIONSHIP_CENTRIC
        elif has_entity_focus:
            return SearchStrategy.ENTITY_CENTRIC
        elif has_semantic_terms:
            return SearchStrategy.SEMANTIC_ONLY
        else:
            return SearchStrategy.HYBRID

    async def _graph_traversal_search(self, query: str, organisation_id: str, user_id: str,
                                    search_type: GitHubSearchType, filters: Dict[str, Any], 
                                    limit: int) -> Dict[str, Any]:
        """Execute structured graph traversal search"""
        start_time = datetime.now()
        
        try:
            # Build Cypher query based on search type and filters
            cypher_query, params = self._build_graph_query(query, organisation_id, user_id, search_type, filters, limit)

            print("cypher query: ", cypher_query)
            print("params: ", params)
            
            # Execute query
            results = execute_read_query(cypher_query, params)
            
            # Process and rank results
            processed_results = self._process_graph_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'graph_traversal',
                'results': processed_results[:limit],
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'metadata': {'cypher_query': cypher_query}
            }
            
        except Exception as e:
            logger.error(f"Graph traversal search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, user_id, limit)
        
    
    async def _vector_search(self, query: str, organisation_id: str, 
                           search_type: GitHubSearchType, filters: Dict[str, Any], 
                           limit: int) -> Dict[str, Any]:
        """Execute semantic vector search"""
        start_time = datetime.now()
        
        try:
            if not self.pinecone_service:
                return await self._fallback_search(query, organisation_id, limit)
            
            # Enhance query with GitHub context
            enhanced_query = self._enhance_query_for_github(query, search_type)
            
            # Execute vector search
            success, message, vector_results = self.pinecone_service.vector_search_only(
                query_text=enhanced_query,
                file_ids=None,  # Search all GitHub content
                top_k=limit * 2  # Get more for filtering
            )
            
            if not success:
                return await self._fallback_search(query, organisation_id, limit)
            
            # Filter results for GitHub entities and organisation
            filtered_results = self._filter_vector_results(vector_results, organisation_id, search_type)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'vector_search',
                'results': filtered_results[:limit],
                'total_count': len(filtered_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _entity_centric_search(self, query: str, organisation_id: str, 
                                   search_type: GitHubSearchType, filters: Dict[str, Any], 
                                   limit: int) -> Dict[str, Any]:
        """Execute entity-focused search"""
        start_time = datetime.now()
        
        try:
            # Extract entities from query
            entities = self._extract_entities_from_query(query)
            
            # Build entity-centric query
            cypher_query = """
            MATCH (e)
            WHERE e.organisation_id = $organisation_id
            AND any(label in labels(e) WHERE label IN $entity_types)
            AND (
                any(prop in keys(e) WHERE toString(e[prop]) CONTAINS $query) OR
                any(entity in $entities WHERE 
                    any(prop in keys(e) WHERE toString(e[prop]) CONTAINS entity)
                )
            )
            WITH e, 
                 CASE 
                   WHEN e.name CONTAINS $query THEN 1.0
                   WHEN e.title CONTAINS $query THEN 0.9
                   WHEN e.description CONTAINS $query THEN 0.7
                   ELSE 0.5
                 END as relevance_score
            
            // Expand to related entities
            OPTIONAL MATCH (e)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
            
            RETURN DISTINCT e as entity, 
                   collect(DISTINCT related)[0..5] as related_entities,
                   collect(DISTINCT type(r))[0..5] as relationship_types,
                   relevance_score
            ORDER BY relevance_score DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'entities': entities,
                'entity_types': list(self.entity_types.values()),
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_entity_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'entity_centric',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Entity-centric search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _relationship_centric_search(self, query: str, organisation_id: str, 
                                         search_type: GitHubSearchType, filters: Dict[str, Any], 
                                         limit: int) -> Dict[str, Any]:
        """Execute relationship-focused search"""
        start_time = datetime.now()
        
        try:
            # Extract relationship patterns from query
            relationship_patterns = self._extract_relationship_patterns(query)
            
            cypher_query = """
            MATCH (source)-[r]->(target)
            WHERE source.organisation_id = $organisation_id
            AND target.organisation_id = $organisation_id
            AND (
                type(r) IN $relationship_types OR
                any(pattern in $patterns WHERE type(r) CONTAINS pattern)
            )
            AND (
                any(prop in keys(source) WHERE toString(source[prop]) CONTAINS $query) OR
                any(prop in keys(target) WHERE toString(target[prop]) CONTAINS $query)
            )
            
            WITH source, r, target,
                 CASE 
                   WHEN type(r) IN $primary_relationships THEN 1.0
                   ELSE 0.7
                 END as relationship_score
            
            RETURN source, type(r) as relationship_type, target, relationship_score
            ORDER BY relationship_score DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'relationship_types': list(self.relationship_types.values()),
                'patterns': relationship_patterns,
                'primary_relationships': ['AUTHORED_BY', 'ASSIGNED_TO', 'OWNS_REPOSITORY'],
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_relationship_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'relationship_centric',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Relationship-centric search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _hybrid_search(self, query: str, organisation_id: str, 
                           search_type: GitHubSearchType, filters: Dict[str, Any], 
                           limit: int) -> Dict[str, Any]:
        """Execute hybrid search combining multiple strategies"""
        start_time = datetime.now()
        
        try:
            # Run multiple searches concurrently
            tasks = [
                self._graph_traversal_search(query, organisation_id, search_type, filters, limit // 2),
                self._vector_search(query, organisation_id, search_type, filters, limit // 2)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Merge and rank results
            merged_results = self._merge_search_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'hybrid',
                'results': merged_results[:limit],
                'total_count': len(merged_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)



    def _build_graph_query(self, query: str, organisation_id: str, user_id: str,
                          search_type: GitHubSearchType, filters: Dict[str, Any], 
                          limit: int) -> Tuple[str, Dict]:
        """Build optimized Cypher query based on search type"""
        
        base_conditions = "n.organisation_id = $organisation_id"
        params = {'organisation_id': organisation_id, 'limit': limit, 'query': query.lower()}
        print("search type : ", search_type.value)
        if user_id:
            params['user_id'] = user_id
        
        if search_type == GitHubSearchType.REPOSITORY:
            cypher_query = f"""
            MATCH (n:GitHubRepository)
            WHERE {base_conditions}
            AND (toLower(n.name) CONTAINS $query OR toLower(n.description) CONTAINS $query 
                 OR toLower(n.full_name) CONTAINS $query)
            {self._add_user_filter('n', user_id) if user_id else ''}
            OPTIONAL MATCH (n)-[:OWNED_BY]->(owner:GitHubUser)
            OPTIONAL MATCH (n)-[:HAS_BRANCH]->(branch:GitHubBranch)
            RETURN n as entity, owner, collect(DISTINCT branch)[0..5] as branches, 'repository' as type,
                   CASE 
                     WHEN toLower(n.name) CONTAINS $query THEN 1.0
                     WHEN toLower(n.full_name) CONTAINS $query THEN 0.9
                     ELSE 0.7
                   END as relevance_score
            ORDER BY relevance_score DESC, n.stars_count DESC, n.updated_at DESC
            LIMIT $limit
            """
            
        elif search_type == GitHubSearchType.PULL_REQUEST:
            # Add filter conditions and parameters
            filter_conditions, filter_params = self._build_filter_conditions(filters)
            params.update(filter_params)
            
            cypher_query = f"""
            MATCH (n:GitHubPullRequest)
            WHERE {base_conditions}
            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)
            {self._add_user_filter('n', user_id) if user_id else ''}
            {filter_conditions}
            OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRepository)
            OPTIONAL MATCH (n)-[:AUTHORED_BY]->(author:GitHubUser)
            OPTIONAL MATCH (n)-[:REVIEWED_BY]->(reviewer:GitHubUser)
            RETURN n as entity, repo, author, collect(DISTINCT reviewer)[0..3] as reviewers, 'pull_request' as type,
                   CASE
                     WHEN toLower(n.title) CONTAINS $query THEN 1.0
                     WHEN toLower(n.body) CONTAINS $query THEN 0.8
                     ELSE 0.6
                   END as relevance_score
            ORDER BY relevance_score DESC, n.updated_at DESC
            LIMIT $limit
            """
            
        elif search_type == GitHubSearchType.COMMIT:
            cypher_query = f"""
            MATCH (n:GitHubCommit)
            WHERE {base_conditions}
            AND (toLower(n.message) CONTAINS $query OR toLower(n.sha) CONTAINS $query)
            {self._add_user_filter('n', user_id) if user_id else ''}
            OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRepository)
            OPTIONAL MATCH (n)-[:AUTHORED_BY]->(author:GitHubUser)
            OPTIONAL MATCH (n)-[:HAS_FILE]->(file:GitHubFile)
            RETURN n as entity, repo, author, collect(DISTINCT file)[0..5] as files, 'commit' as type,
                   CASE 
                     WHEN toLower(n.message) CONTAINS $query THEN 1.0
                     WHEN n.sha STARTS WITH $query THEN 0.9
                     ELSE 0.7
                   END as relevance_score
            ORDER BY relevance_score DESC, n.committed_date DESC
            LIMIT $limit
            """
            
        elif search_type == GitHubSearchType.BRANCH:
            cypher_query = f"""
            MATCH (n:GitHubBranch)
            WHERE {base_conditions}
            AND toLower(n.name) CONTAINS $query
            OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRepository)
            OPTIONAL MATCH (n)-[:HAS_COMMIT]->(commit:GitHubCommit)
            RETURN n as entity, repo, collect(DISTINCT commit)[0..5] as commits, 'branch' as type,
                   CASE 
                     WHEN toLower(n.name) = $query THEN 1.0
                     WHEN toLower(n.name) STARTS WITH $query THEN 0.9
                     ELSE 0.7
                   END as relevance_score
            ORDER BY relevance_score DESC, n.updated_at DESC
            LIMIT $limit
            """
            
        elif search_type == GitHubSearchType.USER:
            cypher_query = f"""
            MATCH (n:GitHubUser)
            WHERE {base_conditions}
            AND (toLower(n.login) CONTAINS $query OR toLower(n.name) CONTAINS $query 
                 OR toLower(n.email) CONTAINS $query)
            OPTIONAL MATCH (n)-[:OWNS_REPOSITORY]->(repo:GitHubRepository)
            OPTIONAL MATCH (n)-[:MEMBER_OF]->(team:GitHubTeam)
            RETURN n as entity, collect(DISTINCT repo)[0..5] as repositories, 
                   collect(DISTINCT team)[0..3] as teams, 'user' as type,
                   CASE 
                     WHEN toLower(n.login) = $query THEN 1.0
                     WHEN toLower(n.name) CONTAINS $query THEN 0.9
                     ELSE 0.7
                   END as relevance_score
            ORDER BY relevance_score DESC, n.followers_count DESC
            LIMIT $limit
            """
            
        elif search_type == GitHubSearchType.TEAM:
            cypher_query = f"""
            MATCH (n:GitHubTeam)
            WHERE {base_conditions}
            AND (toLower(n.name) CONTAINS $query OR toLower(n.description) CONTAINS $query)
            OPTIONAL MATCH (n)<-[:MEMBER_OF]-(member:GitHubUser)
            OPTIONAL MATCH (n)-[:BELONGS_TO]->(org:GitHubOrganization)
            RETURN n as entity, collect(DISTINCT member)[0..10] as members, org, 'team' as type,
                   CASE 
                     WHEN toLower(n.name) = $query THEN 1.0
                     WHEN toLower(n.name) CONTAINS $query THEN 0.9
                     ELSE 0.7
                   END as relevance_score
            ORDER BY relevance_score DESC
            LIMIT $limit
            """
            
        else:  # ALL or generic search
            cypher_query = f"""
            MATCH (n)
            WHERE {base_conditions}
            AND any(label in labels(n) WHERE label STARTS WITH 'GitHub')
            AND (
                (n.name IS NOT NULL AND toLower(n.name) CONTAINS $query) OR
                (n.title IS NOT NULL AND toLower(n.title) CONTAINS $query) OR
                (n.description IS NOT NULL AND toLower(n.description) CONTAINS $query) OR
                (n.login IS NOT NULL AND toLower(n.login) CONTAINS $query) OR
                (n.message IS NOT NULL AND toLower(n.message) CONTAINS $query)
            )
            {self._add_user_filter('n', user_id) if user_id else ''}
            OPTIONAL MATCH (n)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
            RETURN n as entity, labels(n) as entity_types, 
                   collect(DISTINCT related)[0..3] as related_entities, 'generic' as type,
                   CASE 
                     WHEN n.name IS NOT NULL AND toLower(n.name) CONTAINS $query THEN 1.0
                     WHEN n.title IS NOT NULL AND toLower(n.title) CONTAINS $query THEN 0.9
                     WHEN n.login IS NOT NULL AND toLower(n.login) CONTAINS $query THEN 0.8
                     ELSE 0.6
                   END as relevance_score
            ORDER BY relevance_score DESC, n.updated_at DESC
            LIMIT $limit
            """

        return cypher_query, params

    def _add_user_filter(self, node_var: str, user_id: str) -> str:
        """Add user-specific filter to query"""
        if not user_id:
            return ""
        
        return f"""
        AND (
            ({node_var})-[:AUTHORED_BY|OWNED_BY|CREATED_BY]->(:GitHubUser {{id: $user_id}}) OR
            ({node_var})-[:ASSIGNED_TO]->(:GitHubUser {{id: $user_id}}) OR
            ({node_var})-[:REVIEWED_BY]->(:GitHubUser {{id: $user_id}})
        )
        """

    def _build_filter_conditions(self, filters: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """Build Cypher filter conditions from filters dictionary"""
        if not filters:
            return "", {}
        
        conditions = []
        params = {}
        
        # Author filter - filter PRs by specific author
        if 'author' in filters and filters['author']:
            conditions.append("EXISTS((n)-[:AUTHORED_BY]->(:GitHubUser {login: $author_login}))")
            params['author_login'] = filters['author']
        
        # Repository filter - filter PRs by specific repository
        if 'repository' in filters and filters['repository']:
            conditions.append("EXISTS((n)-[:BELONGS_TO]->(:GitHubRepository {name: $repo_name}))")
            params['repo_name'] = filters['repository']
        
        # State filter - filter PRs by state (open, closed, merged)
        if 'state' in filters and filters['state']:
            conditions.append("n.state = $pr_state")
            params['pr_state'] = filters['state']
        
        # Date range filter - filter PRs by creation date
        if 'created_after' in filters and filters['created_after']:
            conditions.append("n.created_at >= datetime($created_after)")
            params['created_after'] = filters['created_after']
        
        if 'created_before' in filters and filters['created_before']:
            conditions.append("n.created_at <= datetime($created_before)")
            params['created_before'] = filters['created_before']
        
        # Combine all conditions with AND
        if conditions:
            return "AND " + " AND ".join(conditions), params
        
        return "", {}

    # Additional helper methods would continue here...
    def _generate_cache_key(self, query: str, organisation_id: str, user_id: str,
                           search_type: GitHubSearchType, strategy: SearchStrategy, 
                           filters: Dict[str, Any]) -> str:
        """Generate cache key for search results"""
        key_data = f"{query}:{organisation_id}:{user_id}:{search_type.value}:{strategy.value}:{json.dumps(filters or {}, sort_keys=True)}"
        return f"github_search:{hashlib.md5(key_data.encode()).hexdigest()}"

    def _process_graph_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process and format graph search results"""
        processed = []
        
        for result in results:
            entity = result.get('entity', {})
            processed_result = {
                'id': entity.get('id') or entity.get('node_id'),
                'type': result.get('type', 'unknown'),
                'title': entity.get('name') or entity.get('title') or entity.get('login'),
                'description': entity.get('description') or entity.get('body', '')[:200],
                'url': entity.get('html_url') or entity.get('url'),
                'score': result.get('relevance_score', 0.0),
                'metadata': {
                    'entity_data': entity,
                    'related_entities': result.get('related_entities', []),
                    'owner': result.get('owner'),
                    'author': result.get('author'),
                    'repository': result.get('repo')
                }
            }
            processed.append(processed_result)
        
        return processed

    async def _fallback_search(self, query: str, organisation_id: str, user_id: str, limit: int) -> Dict[str, Any]:
        """Fallback search using simple property matching"""
        start_time = datetime.now()
        
        try:
            cypher_query = """
            MATCH (n)
            WHERE n.organisation_id = $organisation_id
            AND any(label in labels(n) WHERE label STARTS WITH 'GitHub')
            AND (
                (n.name IS NOT NULL AND toLower(n.name) CONTAINS $query) OR
                (n.title IS NOT NULL AND toLower(n.title) CONTAINS $query) OR
                (n.description IS NOT NULL AND toLower(n.description) CONTAINS $query) OR
                (n.login IS NOT NULL AND toLower(n.login) CONTAINS $query) OR
                (n.message IS NOT NULL AND toLower(n.message) CONTAINS $query)
            )
            RETURN n as entity, labels(n) as entity_types,
                   CASE 
                     WHEN n.name IS NOT NULL AND toLower(n.name) CONTAINS $query THEN 1.0
                     WHEN n.title IS NOT NULL AND toLower(n.title) CONTAINS $query THEN 0.9
                     ELSE 0.7
                   END as relevance_score
            ORDER BY relevance_score DESC, n.updated_at DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_graph_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'fallback',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Fallback search failed: {str(e)}")
            return {
                'success': False,
                'strategy': 'fallback',
                'results': [],
                'total_count': 0,
                'error': str(e)
            }