{"expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields", "id": "59660", "self": "https://rapidinnovation.atlassian.net/rest/api/3/issue/59660", "key": "ARW-8", "fields": {"statuscategorychangedate": "2025-02-13T21:24:09.098-0800", "issuetype": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/issuetype/11085", "id": "11085", "description": "A small, distinct piece of work.", "iconUrl": "https://rapidinnovation.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10318?size=medium", "name": "Task", "subtask": false, "avatarId": 10318, "entityId": "73b204b9-a1b2-4c43-a643-19e4f7d7cd37", "hierarchyLevel": 0}, "timespent": 28800, "project": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/project/10379", "id": "10379", "key": "ARW", "name": "Ruh.ai", "projectTypeKey": "software", "simplified": true, "avatarUrls": {}, "projectCategory": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/projectCategory/10003", "id": "10003", "description": "Agentic workflows for Roadmap ", "name": "Agent-Development"}}, "fixVersions": [], "aggregatetimespent": 28800, "statusCategory": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Done"}, "resolution": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/resolution/10000", "id": "10000", "description": "Work has been completed on this issue.", "name": "Done"}, "resolutiondate": "2025-02-13T21:24:09.067-0800", "workratio": -1, "issuerestriction": {"issuerestrictions": {}, "shouldDisplay": true}, "watches": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/issue/ARW-8/watchers", "watchCount": 2, "isWatching": false}, "lastViewed": null, "created": "2025-02-05T22:30:33.095-0800", "customfield_10020": [{"id": 1334, "name": "ARW Sprint 2", "state": "closed", "boardId": 338, "goal": "CRUD Workflow and Agent Microservices\nSetup communication for orchestration and other micorservices\nAgent Integration with the Workflow through Kafka\n\nComplete the Workflow UI\nWorkflow Builder Integration with Backend\nTesting Agents on the Livekit Room\n\nScript, Audio, Image Clip Generation Code Review\nVideo Clip, Video, Avatar, Subtitle Generation and Code Review\n\nUser, Admin Microservice\n\nArcbot Microservice Backend (User, Admin, Contacts, Campaign, API Gateway)\n\nBlog Generation MCP\nMCP Server Generation\n\nCommunication Microservice\nDynamic Prompt Integration on Livekit Agent and Custom Agent", "startDate": "2025-03-10T06:44:03.431Z", "endDate": "2025-03-25T06:42:00.000Z", "completeDate": "2025-03-10T07:01:39.245Z"}, {"id": 1335, "name": "Sprint 0", "state": "closed", "boardId": 338, "goal": "", "startDate": "2025-02-01T12:24:02.949Z", "endDate": "2025-02-04T12:24:00.000Z", "completeDate": "2025-07-02T12:25:14.821Z"}, {"id": 1001, "name": "ARW Sprint 1", "state": "closed", "boardId": 338, "goal": "", "startDate": "2025-02-06T07:42:15.515Z", "endDate": "2025-02-14T08:00:00.000Z", "completeDate": "2025-03-10T06:41:58.229Z"}], "customfield_10021": null, "priority": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/priority/3", "iconUrl": "https://rapidinnovation.atlassian.net/images/icons/priorities/medium_new.svg", "name": "Medium", "id": "3"}, "labels": [], "customfield_10016": 3.0, "customfield_10019": "2|i00o6n:", "timeestimate": 0, "aggregatetimeoriginalestimate": null, "versions": [], "issuelinks": [], "assignee": {}, "updated": "2025-03-10T22:26:24.728-0700", "status": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/status/11219", "description": "", "iconUrl": "https://rapidinnovation.atlassian.net/", "name": "Done", "id": "11219", "statusCategory": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Done"}}, "components": [], "timeoriginalestimate": null, "description": {"type": "doc", "version": 1, "content": []}, "timetracking": {"remainingEstimate": "0m", "timeSpent": "1d", "remainingEstimateSeconds": 0, "timeSpentSeconds": 28800}, "customfield_10015": "2025-02-06", "security": null, "attachment": [], "aggregatetimeestimate": 0, "summary": "API Gateway kickstart", "creator": {}, "subtasks": [], "reporter": {}, "aggregateprogress": {"progress": 28800, "total": 28800, "percent": 100}, "customfield_10000": "{}", "customfield_10001": null, "environment": null, "duedate": "2025-02-11", "progress": {"progress": 28800, "total": 28800, "percent": 100}, "votes": {}, "comment": {"comments": [], "self": "https://rapidinnovation.atlassian.net/rest/api/3/issue/59660/comment", "maxResults": 4, "total": 4, "startAt": 0}, "worklog": {"startAt": 0, "maxResults": 20, "total": 2, "worklogs": [{"self": "https://rapidinnovation.atlassian.net/rest/api/3/issue/59660/worklog/21424", "author": {"self": "https://rapidinnovation.atlassian.net/rest/api/3/user?accountId=************************", "accountId": "************************", "avatarUrls": {}, "displayName": "<PERSON><PERSON><PERSON>", "active": true, "timeZone": "America/Los_Angeles", "accountType": "atlassian"}, "updateAuthor": {}, "created": "2025-02-06T21:49:24.169-0800", "updated": "2025-02-06T21:49:24.169-0800", "started": "2025-02-06T10:00:00.000-0800", "timeSpent": "3h", "timeSpentSeconds": 10800, "id": "21424", "issueId": "59660"}, {"self": "https://rapidinnovation.atlassian.net/rest/api/3/issue/59660/worklog/21444", "author": {}, "updateAuthor": {}, "comment": {"type": "doc", "version": 1, "content": []}, "created": "2025-02-09T19:26:34.881-0800", "updated": "2025-02-09T19:26:34.881-0800", "started": "2025-02-09T14:26:06.766-0800", "timeSpent": "5h", "timeSpentSeconds": 18000, "id": "21444", "issueId": "59660"}]}}}